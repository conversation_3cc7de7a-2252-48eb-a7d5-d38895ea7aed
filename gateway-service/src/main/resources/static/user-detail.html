<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>User Details</title>
    <link rel="icon" href="data:;base64,iVBORw0KGgo=">
    <link rel="stylesheet" href="css/user-detail.css">
    <link rel="stylesheet" href="css/user-detail-like.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sockjs-client/1.5.0/sockjs.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/stomp.js/2.3.3/stomp.min.js"></script>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>User Details</h1>
        <button class="back-button" onclick="goBack()">Back to List</button>
    </div>
    <div id="userDetail" class="user-detail">
        <!-- 사용자 정보가 여기에 동적으로 추가됩니다 -->
    </div>

    <div class="tabs">
        <div class="tab-buttons">
            <button class="tab-button active" onclick="showTab('matching')">Matching</button>
            <button class="tab-button" onclick="showTab('like')">Like</button>
            <button class="tab-button" onclick="showTab('chat')">Chat</button>
            <button class="tab-button" onclick="showTab('portfolio')">Portfolio</button>
            <button class="tab-button" onclick="showTab('cs')">CS</button>
        </div>

        <div id="matching" class="tab-content active">
            <div class="search-bar">
                <input type="text" id="matchingSearch" placeholder="Search matched users...">
                <button onclick="searchMatching()">Search</button>
            </div>
            <div class="list-container" id="matchingList">
                <!-- 매칭 목록이 여기에 추가됩니다 -->
            </div>
        </div>

        <div id="like" class="tab-content">
            <div class="likes-container">
                <div class="like-section">
                    <h2>They Like</h2>
                    <div class="like-search">
                        <input type="text" id="likeMeSearch" placeholder="Search people who liked you">
                        <button id="likeMeSearchButton">Search</button>
                    </div>
                    <div class="like-list" id="likeMeList">
                        <table class="like-table" id="likeMeTable">
                            <thead>
                            <tr>
                                <th>Id</th>
                                <th>Name</th>
                                <th>Date</th>
                            </tr>
                            </thead>
                            <tbody id="likeMeTableBody">
                            <!-- JS 로 채워집니다 -->
                            </tbody>
                        </table>                    </div>
                    <button id="likeMeLoadMore" class="nav-button" onclick="loadMoreLikes('likeMe')">더보기</button>
                </div>
                <div class="like-section">
                    <h2>I Like</h2>
                    <div class="like-search">
                        <input type="text" id="iLikeSearch" placeholder="Search people you liked">
                        <button id="iLikeSearchButton">Search</button>
                    </div>
                    <div class="like-list" id="iLikeList">
                        <table class="like-table" id="iLikeTable">
                            <thead>
                            <tr>
                                <th>Id</th>
                                <th>Name</th>
                                <th>Date</th>
                            </tr>
                            </thead>
                            <tbody id="iLikeTableBody">
                            <!-- JS 로 채워집니다 -->
                            </tbody>
                        </table>                    </div>
                    <button id="iLikeLoadMore" class="nav-button" onclick="loadMoreLikes('iLike')">더보기</button>
                </div>
            </div>
        </div>

        <div id="chat" class="tab-content">
            <div class="chat-container">
                <div class="chat-rooms-list">
                    <div class="search-bar">
                        <input type="text" id="chatRoomSearch" placeholder="채팅방 검색..." oninput="searchChatRooms()">
                    </div>
                    <div id="chatRoomsList">
                        <!-- 채팅방 목록이 여기에 추가됩니다 -->
                    </div>
                </div>
                <div class="chat-messages" id="chatMessages">
                    <div class="messages-header">
                        <div class="messages-header-content">
                            <h3 id="selectedRoomTitle">채팅방을 선택해주세요</h3>
                            <div class="search-bar">
                                <input type="text" id="messageSearch" placeholder="메시지 검색..." oninput="searchMessages()">
                                <div class="search-navigation">
                                    <button class="nav-button" id="prevResult" disabled onclick="navigateSearchResults(-1)">↑</button>
                                    <span class="search-count" id="searchCount"></span>
                                    <button class="nav-button" id="nextResult" disabled onclick="navigateSearchResults(1)">↓</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="messages-content" id="messagesList">
                        <!-- 채팅 메시지가 여기에 추가됩니다 -->
                    </div>
                </div>
            </div>
        </div>

        <div id="portfolio" class="tab-content">
            <div id="portfolioList" class="portfolio-grid">
                <!-- 포트폴리오 목록이 여기에 추가됩니다 -->
            </div>
        </div>

        <div id="cs" class="tab-content">
            <div id="csList">
                <div class="chat-room-container">
                    <div class="chat-room-header">
                        <h3>CS 채팅방</h3>
                    </div>
                    <div id="cs-messages" class="messages-list">
                        <!-- CS 채팅 메시지가 여기에 추가됩니다 -->
                    </div>
                    <div class="message-input-container">
                        <input type="text" class="message-input" id="csMessageInput" placeholder="메시지를 입력하세요...">
                        <button class="send-button" onclick="sendCSMessage()">
                            <i class="fas fa-paper-plane"></i>
                            전송
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 이미지 모달 추가 -->
<!-- body 맨 아래에 한 번만 남겨두세요 -->
<div id="imageModal" class="modal" style="display:none">
    <button class="modal-close" onclick="closeModal()">&times;</button>
    <button class="modal-nav modal-prev" onclick="changeImage(-1)">&#10094;</button>
    <img id="modalImg" class="modal-img" src="" alt="확대 이미지">
    <button class="modal-nav modal-next" onclick="changeImage(1)">&#10095;</button>
</div>

<script src="js/config.js"></script>
<script src="js/user-detail.js"></script>

</body>
</html>
