/* =============== Reset & Base =============== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
body {
  font-family: 'Segoe UI', Tahoma, sans-serif;
  background: #f5f6fa;
  color: #333;
  line-height: 1.4;
}
a {
  color: inherit;
  text-decoration: none;
}
button {
  font-family: inherit;
}

/* =============== Container & Header =============== */
.container {
  max-width: 1200px;
  margin: 20px auto;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}
.header h1 {
  font-size: 1.75rem;
  font-weight: 600;
}
.back-button {
  padding: 8px 16px;
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background .2s;
}
.back-button:hover {
  background: #0056b3;
}

/* =============== Profile Section =============== */
.profile-section {
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 24px;
  margin-bottom: 24px;
}
.profile-header {
  display: flex;
  align-items: flex-start;
}
.profile-left {
  flex: 0 0 200px;
  text-align: center;
}
.profile-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #ddd;
}
.profile-name {
  margin-top: 12px;
  font-size: 1.25rem;
  font-weight: 600;
}
.profile-email {
  margin-top: 4px;
  font-size: 0.9rem;
  color: #666;
}
.profile-right {
  flex: 1;
  padding-left: 24px;
}
.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}
.info-item strong {
  width: 120px;
  display: block;
  font-weight: 600;
}
.info-item .tag-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* =============== Tag Styles =============== */
.tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
}
.purpose-tag {
  background: #e8f4fd;
  color: #0275d8;
}
.talent-tag-design {
  background: #e0f7e9;
  color: #2e7d32;
}
.talent-tag-art {
  background: #fff4e5;
  color: #ff8f00;
}
.talent-tag-media {
  background: #e8eaf6;
  color: #3949ab;
}

/* =============== Tabs =============== */
.tabs {
  margin-top: 24px;
}
.tab-buttons {
  display: flex;
  border-bottom: 2px solid #e0e0e0;
}
.tab-button {
  padding: 12px 20px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 1rem;
  color: #555;
  transition: color .2s;
}
.tab-button:hover {
  color: #000;
}
.tab-button.active {
  color: #007bff;
  border-bottom: 2px solid #007bff;
}
.tab-content {
  display: none;
  padding: 20px 0;
}
.tab-content.active {
  display: block;
}

/* =============== Search Bar =============== */
.search-bar {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}
.search-bar input[type="text"] {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.search-bar button {
  padding: 8px 14px;
  border: none;
  background: #007bff;
  color: #fff;
  border-radius: 4px;
  cursor: pointer;
  transition: background .2s;
}
.search-bar button:hover {
  background: #0056b3;
}

/* =============== Matching List =============== */
.match-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background .2s;
}
.match-item:hover {
  background: #f9f9f9;
}
.match-with {
  font-weight: 600;
  margin-right: 12px;
  color: #555;
}
.match-name {
  flex: 1;
  font-size: 1rem;
  color: #333;
}
.match-date-container {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
  color: #777;
}
.match-number {
  background: #eee;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 0.75rem;
}

/* =============== Like Sections =============== */
.likes-container {
  display: flex;
  gap: 24px;
}
.like-section {
  flex: 1;
  background: #fafafa;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
.like-list .like-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background .2s;
}
.like-list .like-item:hover {
  background: #f0f8ff;
}
.like-item img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 12px;
}
.like-info {
  display: flex;
  flex-direction: column;
}
.like-name {
  font-size: 1rem;
  font-weight: 500;
}
.like-date {
  font-size: 0.85rem;
  color: #666;
}

/* =============== Chat (Admin-CS) =============== */
.chat-container {
  display: flex;
  gap: 24px;
}
.chat-rooms-list {
  flex: 0 0 300px;
  border-right: 1px solid #e0e0e0;
  padding-right: 16px;
}
.chat-room {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background .2s;
}
.chat-room:hover {
  background: #f9f9f9;
}
.chat-room-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 12px;
}
.chat-room-info {
  flex: 1;
}
.chat-room-title {
  font-size: 1rem;
  margin-bottom: 4px;
}
.unread-badge {
  background: #dc3545;
  color: #fff;
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 12px;
  margin-left: 8px;
}
.chat-room-message,
.chat-room-participants {
  font-size: 0.85rem;
  color: #666;
  margin-top: 2px;
}

/* =============== Chat Messages =============== */
.messages-content {
  flex: 1;
  height: 500px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0 12px;
}
.message {
  max-width: 60%;
  padding: 12px;
  border-radius: 8px;
  position: relative;
}
.message.sent {
  align-self: flex-end;
  background: #007bff;
  color: #fff;
  border-bottom-right-radius: 2px;
}
.message.received {
  align-self: flex-start;
  background: #f1f0f0;
  color: #333;
  border-bottom-left-radius: 2px;
}
.message-time {
  position: absolute;
  bottom: -20px;
  right: 8px;
  font-size: 0.7rem;
  color: #555;
}

/* =============== Portfolio Grid =============== */
#portfolioList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  padding: 20px;
}

.portfolio-item {
  aspect-ratio: 1;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
}

.portfolio-item:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.portfolio-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.portfolio-item:hover img {
  transform: scale(1.1);
}

/* =============== CS Chat Room =============== */
.chat-room-container {
  display: flex;
  flex-direction: column;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  height: 600px;
  overflow: hidden;
}
.messages-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #fff;
}
.message-input-container {
  display: flex;
  padding: 12px;
  border-top: 1px solid #e0e0e0;
}
.message-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.send-button {
  padding: 8px 14px;
  margin-left: 8px;
  background: #28a745;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background .2s;
}
.send-button:hover {
  background: #218838;
}

/* =============== Modal (Image Preview) =============== */
.modal {
  display: none;
  position: fixed;
  inset: 0;
  background: rgba(0,0,0,0.8);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-img {
  max-width: 90%;
  max-height: 90%;
  border-radius: 4px;
}

.modal-close {
  position: absolute;
  top: -40px;
  right: 0;
  background: none;
  border: none;
  color: white;
  font-size: 30px;
  cursor: pointer;
  padding: 5px 10px;
}

.modal-prev,
.modal-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.3);
  border: none;
  color: white;
  font-size: 24px;
  padding: 15px;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.modal-prev:hover,
.modal-next:hover {
  background: rgba(255, 255, 255, 0.5);
}

.modal-prev {
  left: 20px;
}

.modal-next {
  right: 20px;
}

.portfolio-item {
  cursor: pointer;
  transition: transform 0.3s;
}

.portfolio-item:hover {
  transform: scale(1.05);
}

.portfolio-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}
