/* — Like 탭 2-컬럼 레이아웃 — */
.like-content {
  display: flex;
  gap: 40px;
  margin-top: 24px;
}
.like-section {
  flex: 1;
}
.like-section h2 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 12px;
}
.like-search {
  display: flex;
  margin-bottom: 12px;
}
.like-search input {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
}
.like-search button {
  margin-left: 8px;
  padding: 6px 12px;
  background: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
.like-search button:hover {
  background: #0056b3;
}
.like-table {
  width: 100%;
  border-collapse: collapse;
}
.like-table th,
.like-table td {
  padding: 8px 6px;
  border-bottom: 1px solid #e0e0e0;
  text-align: left;
}
.avatar {
  display: inline-block;
  width: 32px;
  height: 32px;
  margin-right: 8px;
  border-radius: 50%;
  overflow: hidden;
  background: #eee;
  vertical-align: middle;
}
.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}
.name-cell a {
  vertical-align: middle;
  line-height: 32px;
  font-weight: 500;
  color: #007bff;
  text-decoration: none;
}
.name-cell a:hover {
  text-decoration: underline;
}
