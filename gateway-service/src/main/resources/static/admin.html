<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="data:;base64,iVBORw0KGgo=">
    <title>관리자 로그인</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .login-container {
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 0.75rem;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
        }
        button:hover {
            background-color: #0056b3;
        }
        .error-message {
            color: red;
            margin-top: 1rem;
            display: none;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2 style="text-align: center; margin-bottom: 2rem;">관리자 로그인</h2>
        <form id="loginForm">
            <div class="form-group">
                <label for="email">이메일</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="password">비밀번호</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit">로그인</button>
            <div id="errorMessage" class="error-message">
                로그인에 실패했습니다. 이메일과 비밀번호를 확인해주세요.
            </div>
        </form>
    </div>

    <script src="js/config.js"></script>
    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch(`${CONFIG.API_BASE_URL}/api/v1/users/signin`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('Login response:', data);  // 응답 데이터 확인
                    
                    // 로그인 성공 시 토큰을 localStorage에 저장
                    localStorage.setItem('accessToken', data.accessToken);
                    localStorage.setItem('refreshToken', data.refreshToken);
                    localStorage.setItem('userId', data.userId);
                    
                    console.log('Stored tokens:', {  // localStorage 저장 확인
                        accessToken: localStorage.getItem('accessToken'),
                        refreshToken: localStorage.getItem('refreshToken'),
                        userId: localStorage.getItem('userId')
                    });
                    
                    // 관리자 대시보드 페이지로 이동
                    window.location.href = 'admin-dashboard.html';
                } else {
                    const errorData = await response.json();
                    console.error('Login failed:', errorData);  // 에러 응답 확인
                    document.getElementById('errorMessage').style.display = 'block';
                }
            } catch (error) {
                console.error('Login error:', error);
                document.getElementById('errorMessage').style.display = 'block';
            }
        });
    </script>
</body>
</html>
