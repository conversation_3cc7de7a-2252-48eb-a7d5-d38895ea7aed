// js/user-detail.js

// ———————————————————————————————————————
// Constants & Helpers
// ———————————————————————————————————————
const CONFIG_API_BASE = CONFIG.API_BASE_URL;
const CONFIG_CHAT_BASE = CONFIG.API_CHAT_BASE_URL;
const PAGE_SIZE = 10;

// Mapping data
const PURPOSE_MAP = {
  0: 'Get Along With U',
  1: 'Want to Collaborate',
  2: 'Wanna Make New Brand',
  3: 'Art Residency',
  4: 'Group Exhibition'
};
function formatPurposeName(name) {
  return name.split('_')
    .map(w => w.charAt(0).toUpperCase() + w.slice(1).toLowerCase())
    .join(' ');
}

const TALENT_DISPLAY_MAP = {
  '산업 디자인': 'Industrial Design',
  '그래픽 디자인': 'Graphic Design',
  '패션 디자인': 'Fashion Design',
  'UX/UI 디자인': 'UX/UI Design',
  '브랜딩': 'Branding',
  '모션 그래픽': 'Motion Graphic',
  '애니메이션': 'Animation',
  '일러스트레이션': 'Illustration',
  '인테리어 디자인': 'Interior Design',
  '건축 디자인': 'Architecture Design',
  '텍스타일': 'Textile',
  '패브릭 제품': 'Fabric Product',
  '스타일링': 'Styling',
  '가방 디자인': 'Bag Design',
  '신발 디자인': 'Shoes Design',
  '회화': 'Painting',
  '조소': 'Sculpture',
  '키네틱 아트': 'Kinetic Art',
  '도자기': 'Ceramics',
  '목공': 'Woodworking',
  '주얼리': 'Jewelry',
  '금속 공예': 'Metal Craft',
  '유리 공예': 'Glass Craft',
  '판화': 'Printmaking',
  '미학': 'Aesthetics',
  '터프팅': 'Tufting',
  '시인': 'Poet',
  '글쓰기': 'Writing',
  '사진': 'Photography',
  '광고': 'Advertising',
  '시나리오': 'Scenario',
  '작곡': 'Music Composition',
  '감독': 'Director',
  '춤': 'Dance',
  '노래': 'Singing',
  '뮤지컬': 'Musical',
  '코미디': 'Comedy',
  '연기': 'Acting',
  '제작': 'Production'
};
const DESIGN_TALENTS = ['산업 디자인','그래픽 디자인','패션 디자인','UX/UI 디자인','브랜딩','모션 그래픽','애니메이션','일러스트레이션','인테리어 디자인','건축 디자인','텍스타일','패브릭 제품','스타일링','가방 디자인','신발 디자인'];
const ART_CRAFT_TALENTS = ['회화','조소','키네틱 아트','도자기','목공','주얼리','금속 공예','유리 공예','판화','미학','터프팅'];
const MEDIA_CONTENT_TALENTS = ['시인','글쓰기','사진','광고','시나리오','작곡','감독','춤','노래','뮤지컬','코미디','연기','제작'];

function getTalentCategoryClass(name) {
  if (DESIGN_TALENTS.includes(name)) return 'talent-tag-design';
  if (ART_CRAFT_TALENTS.includes(name)) return 'talent-tag-art';
  if (MEDIA_CONTENT_TALENTS.includes(name)) return 'talent-tag-media';
  return 'talent-tag';
}

// Fetch helper with auth expiry & error handling
async function apiFetch(url, opts = {}) {
  const res = await fetch(url, opts);
  if (res.status === 401 || res.status === 403) {
    alert('인증이 만료되었습니다. 다시 로그인해주세요.');
    window.location.href = 'admin.html';
    throw new Error('AUTH_EXPIRED');
  }
  if (!res.ok) {
    const text = await res.text();
    throw new Error(`[${res.status}] ${text}`);
  }
  return await res.json();
}

// ———————————————————————————————————————
// Auth & Init
// ———————————————————————————————————————
const token = localStorage.getItem('accessToken');
if (!token) window.location.href = 'admin.html';

const urlParams = new URLSearchParams(window.location.search);
const userId = urlParams.get('userId');
if (!userId) {
  alert('사용자 ID가 필요합니다.');
  goBack();
}

let portfolioItems = [];
let currentImageIndex = 0;
let currentChatRoom = null;
let stompClient = null;
let currentCsRoomId = null;

// Pagination & search state
let likeMePage = 0, iLikePage = 0;
let likeMeHasMore = true, iLikeHasMore = true;
let likeMeSearch = '', iLikeSearch = '';

// On load
window.onload = async () => {
  initializeLikeSearch();
  await Promise.all([
    loadUserDetail(),
    loadMatching(),
    loadLikes(),
    loadChats(),
    loadCS()
  ]);
};

// ———————————————————————————————————————
// Tab switching
// ———————————————————————————————————————
const tabCallbacks = {};
function onShowTab(tab, cb) { tabCallbacks[tab] = cb; }

function showTab(name) {
  document.querySelectorAll('.tab-content').forEach(el => el.classList.remove('active'));
  document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
  document.getElementById(name).classList.add('active');
  document.querySelector(`[onclick="showTab('${name}')"]`).classList.add('active');
  if (tabCallbacks[name]) tabCallbacks[name]();
}
onShowTab('cs', () => {
  if (stompClient) { stompClient.disconnect(); stompClient = null; }
  loadCS();
});
onShowTab('portfolio', renderPortfolio);

// ———————————————————————————————————————
// User Detail
// ———————————————————————————————————————
async function loadUserDetail() {
  try {
    const user = await apiFetch(`${CONFIG_API_BASE}/api/v1/users/admin/users/${userId}`, {
      headers: { 'Authorization': token }
    });
    portfolioItems = user.userPortfolio?.portfolioItems?.map(i => i.itemUrl) || [];
    renderPortfolio();
    renderProfile(user);
  } catch (e) {
    console.error('loadUserDetail', e);
    alert('사용자 정보를 불러오는데 실패했습니다.');
  }
}

function renderProfile(u) {
  const container = document.getElementById('userDetail');
  container.textContent = '';

  const section = document.createElement('div'); section.className = 'profile-section';
  const header  = document.createElement('div'); header.className = 'profile-header';
  section.append(header);

  // Left
  const left = document.createElement('div'); left.className = 'profile-left';
  const img  = document.createElement('img'); img.className = 'profile-image';
  img.src = portfolioItems[0] || 'default-profile.png';
  const nameDiv  = document.createElement('div'); nameDiv.className = 'profile-name';
  nameDiv.textContent = u.username || 'No Name';
  const emailDiv = document.createElement('div'); emailDiv.className = 'profile-email';
  emailDiv.textContent = u.email || 'None';
  left.append(img, nameDiv, emailDiv);
  header.append(left);

  // Right
  const right = document.createElement('div'); right.className = 'profile-right';
  function addInfo(label, node) {
    const item = document.createElement('div'); item.className = 'info-item';
    const strong = document.createElement('strong'); strong.textContent = label + ' ';
    item.append(strong, node);
    right.append(item);
  }
  addInfo('Email:', document.createTextNode(u.email || 'None'));
  addInfo('Introduction:', document.createTextNode(u.description || 'None'));
  addInfo('Instagram:', document.createTextNode(u.instagramId ? '@' + u.instagramId : 'None'));
  addInfo('Website:',
    u.webUrl
      ? (() => { const a = document.createElement('a'); a.href = u.webUrl; a.target = '_blank'; a.textContent = u.webUrl; return a; })()
      : document.createTextNode('None')
  );
  // Purpose tags
  const pcont = document.createElement('div'); pcont.className = 'tag-container';
  u.userPurposes.forEach(p => {
    const span = document.createElement('span'); span.classList.add('tag','purpose-tag');
    span.textContent = formatPurposeName(PURPOSE_MAP[p]);
    pcont.append(span);
  });
  addInfo('Purpose:', pcont);

  // Talent tags
  const tcont = document.createElement('div'); tcont.className = 'tag-container';
  u.userTalents.forEach(t => {
    const span = document.createElement('span'); span.classList.add('tag', getTalentCategoryClass(t.talentType));
    span.textContent = TALENT_DISPLAY_MAP[t.talentType] || t.talentType;
    tcont.append(span);
  });
  addInfo('Talent:', tcont);

  section.append(right);
  container.append(section);
}

// ———————————————————————————————————————
// Portfolio
// ———————————————————————————————————————
function renderPortfolio() {
  const el = document.getElementById('portfolioList');
  el.textContent = '';
  if (!portfolioItems.length) {
    const p = document.createElement('p'); p.textContent = '포트폴리오 이미지가 없습니다.';
    el.append(p);
    return;
  }
  portfolioItems.forEach((src, i) => {
    const div = document.createElement('div'); div.className = 'portfolio-item';
    div.addEventListener('click', () => showImage(i));
    const img = document.createElement('img'); img.src = src; img.alt = '포트폴리오 이미지'; img.loading = 'lazy';
    div.append(img);
    el.append(div);
  });
}

function showImage(idx) {
  currentImageIndex = idx;
  const modal = document.getElementById('imageModal');
  const img   = document.getElementById('modalImg');
  img.src      = portfolioItems[idx];
  modal.style.display = 'flex';
}
function changeImage(dir) { showImage((currentImageIndex + dir + portfolioItems.length) % portfolioItems.length); }
function closeModal() { document.getElementById('imageModal').style.display = 'none'; }
document.addEventListener('keydown', e => { if (e.key==='Escape') closeModal(); });

// ———————————————————————————————————————
// Matching
// ———————————————————————————————————————
async function loadMatching() {
  try {
    const list = await apiFetch(`${CONFIG_API_BASE}/api/v1/users/admin/users/${userId}/matched`, { headers:{'Authorization':token} });
    displayMatches(list);
  } catch(e) {
    console.error('loadMatching', e);
    const c = document.getElementById('matchingList');
    c.textContent = ''; const p = document.createElement('p'); p.textContent = '매칭 정보를 불러오는데 실패했습니다.'; c.append(p);
  }
}
function displayMatches(list) {
  const c = document.getElementById('matchingList');
  c.textContent = '';
  if (!Array.isArray(list) || !list.length) {
    const p = document.createElement('p'); p.textContent = '매칭된 사용자가 없습니다.'; c.append(p); return;
  }
  list.forEach((m,i) => {
    const item = document.createElement('div'); item.className='match-item';
    item.addEventListener('click',()=>showUserDetail(m.userId));
    const w = document.createElement('div'); w.className='match-with'; w.textContent='With';
    const n = document.createElement('div'); n.className='match-name'; n.textContent=m.name;
    const dv = document.createElement('div'); dv.className='match-date-container';
    const ds = document.createElement('span'); ds.textContent=new Date(m.matchedAt).toLocaleString('ko-KR',{year:'numeric',month:'2-digit',day:'2-digit',hour:'2-digit',minute:'2-digit'});
    const ns = document.createElement('span'); ns.className='match-number'; ns.textContent=`#${list.length-i}`;
    dv.append(ds,ns); item.append(w,n,dv); c.append(item);
  });
}
function searchMatching() {
  const term = document.getElementById('matchingSearch').value.toLowerCase();
  document.querySelectorAll('.match-item').forEach(it => {
    const nm = it.querySelector('.match-name').textContent.toLowerCase();
    it.style.display = nm.includes(term) ? '' : 'none';
  });
}

// ———————————————————————————————————————
// Likes
// ———————————————————————————————————————
async function loadLikes(type=null, loadMore=false) {
  if (!loadMore) {
    if (type==='likeMe'||type===null) { likeMePage=0; likeMeHasMore=true; }
    if (type==='iLike'||type===null) { iLikePage=0; iLikeHasMore=true; }
  }
  try {
    await Promise.all([
      loadLikePage({
        endpoint: 'they-like',
        listId:   'likeMeList',
        page:     likeMePage,
        hasMore:  likeMeHasMore,
        search:   likeMeSearch,
        btnId:    'likeMeLoadMore',
        append:   loadMore
      }),
      loadLikePage({
        endpoint: 'i-liked',
        listId:   'iLikeList',
        page:     iLikePage,
        hasMore:  iLikeHasMore,
        search:   iLikeSearch,
        btnId:    'iLikeLoadMore',
        append:   loadMore
      })
    ]);
  } catch(e) {
    console.error('loadLikes',e);
    ['likeMeList','iLikeList'].forEach(id => {
      const c=document.getElementById(id); c.textContent=''; const d=document.createElement('div');
      d.className='error-message'; d.textContent='좋아요 데이터를 불러오는데 실패했습니다.'; c.append(d);
    });
  }
}

async function loadLikePage({
  endpoint,   // 'they-like' or 'i-liked'
  listId,     // 'likeMeList' or 'iLikeList'
  page,       // likeMePage / iLikePage (숫자)
  hasMore,    // likeMeHasMore / iLikeHasMore (boolean)
  search,     // likeMeSearch / iLikeSearch (string)
  btnId,      // 버튼 ID
  append      // loadMore flag
}) {
  if (!hasMore) return;

  const url =
    `${CONFIG.API_BASE_URL}/api/v1/users/admin/users/${userId}/likes/${endpoint}` +
    `?page=${page}&size=${PAGE_SIZE}` +
    (search ? `&name=${encodeURIComponent(search)}` : '');

  const container = document.getElementById(listId);
  container.textContent = append ? container.textContent : '';
  try {
      const data = await apiFetch(url,{headers:{'Authorization':token}});
      const items = Array.isArray(data) ? data : (data[listId] || []);

      if (!items.length && !append) {
        const no = document.createElement('div');
        no.className='no-likes';
        no.textContent='검색 결과가 없습니다.';
        container.append(no);
      } else {
        items.forEach(like => {
          const item = document.createElement('div'); item.className='like-item';
          item.addEventListener('click',()=>showUserDetail(like.userId));
          const img = document.createElement('img'); img.src = like.portfolioImageUrl||'default-profile.png'; img.alt='포트폴리오 이미지';
          const info = document.createElement('div'); info.className='like-info';
          const name = document.createElement('div'); name.className='like-name'; name.textContent = like.name||'No Name';
          const date = document.createElement('div'); date.className='like-date';
          date.textContent = new Intl.DateTimeFormat('ko-KR',{year:'numeric',month:'long',day:'numeric'}).format(new Date(like.likedAt));
          info.append(name,date); item.append(img,info); container.append(item);
        });
      }
      const more = items.length === PAGE_SIZE;
      document.getElementById(btnId).style.display = more ? 'block' : 'none';
      if (listId === 'likeMeList') {
        likeMeHasMore = more;
        if (more) likeMePage++;
      } else {
        iLikeHasMore = more;
        if (more) iLikePage++;
      }
    } catch(err) {
    console.error(`${endpoint} loadLikePage`,err);
    container.textContent='';
    const d=document.createElement('div');
    d.className='error-message';
    d.textContent=`데이터 로드 실패: ${err.message}`;
    container.append(d);
    document.getElementById(btnId).style.display='none';
    throw err;
  }
}

function initializeLikeSearch() {
  ['likeMe','iLike'].forEach(type=>{
    const inp=document.getElementById(type+'Search');
    const btn=document.getElementById(type+'SearchButton');
    if (inp && btn) {
      btn.onclick = ()=>{ window[type+'Search']=inp.value.trim(); loadLikes(type); };
      inp.addEventListener('keyup',e=>{ if(e.key==='Enter') btn.click(); });
    }
  });
}
function loadMoreLikes(type){ loadLikes(type,true); }

// ———————————————————————————————————————
// Chat list & Room
// ———————————————————————————————————————
async function loadChats() {
  try {
    const rooms = await apiFetch(`${CONFIG_API_BASE}/api/v1/users/admin/chatroom?userId=${userId}`,{headers:{'Authorization':token}});
    renderChatRooms(rooms);
  } catch(e) {
    console.error('loadChats',e);
    const c=document.getElementById('chatRoomsList'); c.textContent=''; const p=document.createElement('p');
    p.textContent='채팅방 목록을 불러오는데 실패했습니다.'; c.append(p);
  }
}
function renderChatRooms(rooms) {
  const c=document.getElementById('chatRoomsList'); c.textContent='';
  if (!Array.isArray(rooms)||!rooms.length) {
    const p=document.createElement('p'); p.textContent='채팅방이 없습니다.'; c.append(p); return;
  }
  rooms.forEach(r=>{
    const div=document.createElement('div'); div.className='chat-room'; div.dataset.roomId=r.id;
    div.addEventListener('click',()=>loadChatRoom(r.id));
    const img=document.createElement('img'); img.className='chat-room-thumbnail'; img.src=r.chatRoomThumbnail;
    const info=document.createElement('div'); info.className='chat-room-info';
    const hdr=document.createElement('div'); hdr.className='chat-room-header';
    const title=document.createElement('h3'); title.className='chat-room-title'; title.textContent=r.title;
    hdr.append(title);
    if(r.unreadMessageCount>0){
      const badge=document.createElement('span'); badge.className='unread-badge'; badge.textContent=r.unreadMessageCount;
      hdr.append(badge);
    }
    const msg=document.createElement('p'); msg.className='chat-room-message'; msg.textContent=r.latestMessageContent||'새로운 채팅방이 생성되었습니다';
    const part=document.createElement('p'); part.className='chat-room-participants'; part.textContent='참여자: '+r.participants.join(', ');
    info.append(hdr,msg,part); div.append(img,info); c.append(div);
  });
}

async function loadChatRoom(roomId) {
  document.querySelectorAll('.chat-room').forEach(e=>e.classList.remove('active'));
  document.querySelector(`[data-room-id="${roomId}"]`)?.classList.add('active');
  try {
    const chatRoom = await apiFetch(`${CONFIG_API_BASE}/api/v1/users/admin/rooms/${roomId}`,{headers:{'Authorization':token}});
    currentChatRoom = chatRoom;
    document.getElementById('selectedRoomTitle').textContent = chatRoom.title;
    renderMessages(chatRoom.messages, parseInt(userId,10));
    document.getElementById('messageSearch').value='';
  } catch(e) {
    console.error('loadChatRoom',e);
  }
}

function renderMessages(msgs, meId) {
  const list = document.getElementById('messagesList'); list.textContent='';
  if(!Array.isArray(msgs)||!msgs.length){
    const p=document.createElement('p'); p.textContent='메시지가 없습니다.'; list.append(p);
  } else {
    msgs.forEach(m=>{
      const div=document.createElement('div'); div.className='message '+(m.senderId===meId?'sent':'received');
      const cdiv=document.createElement('div'); cdiv.className='message-content'; cdiv.textContent=m.content;
      const tdiv=document.createElement('div'); tdiv.className='message-time';
      tdiv.textContent=new Date(m.createdAt).toLocaleString('ko-KR',{hour:'2-digit',minute:'2-digit'});
      const st=document.createElement('span'); st.className='message-status'; st.textContent=m.readStatus?'✓✓':'✓';
      tdiv.append(st); div.append(cdiv,tdiv); list.append(div);
    });
  }
  list.scrollTop = list.scrollHeight;
}

function searchChatRooms() {
  const term=document.getElementById('chatRoomSearch').value.toLowerCase();
  document.querySelectorAll('.chat-room').forEach(r=>{
    const text = (r.querySelector('.chat-room-title').textContent +
                  r.querySelector('.chat-room-message').textContent +
                  r.querySelector('.chat-room-participants').textContent).toLowerCase();
    r.style.display = text.includes(term) ? '' : 'none';
  });
}

// ———————————————————————————————————————
// CS Chat (WebSocket)
// ———————————————————————————————————————
async function loadCS() {
  const csList = document.getElementById('csList'); csList.textContent='';
  try {
    const chatRoom = await apiFetch(`${CONFIG_API_BASE}/api/v1/users/admin/cs?userId=${userId}`,{headers:{'Authorization':token}});
    if (!chatRoom?.id) {
      const p=document.createElement('p'); p.textContent='CS 채팅방을 찾을 수 없습니다.'; csList.append(p); return;
    }
    currentCsRoomId = chatRoom.id;
    // build DOM...
    const container = document.createElement('div'); container.className='chat-room-container';
    const header = document.createElement('div'); header.className='chat-room-header';
    const title = document.createElement('h3'); title.textContent=chatRoom.title;
    header.append(title);
    const msgsDiv = document.createElement('div'); msgsDiv.id='cs-messages'; msgsDiv.className='messages-list';
    if (chatRoom.messages?.length) {
      chatRoom.messages.forEach(m => showCsMessage(m, msgsDiv));
    } else {
      const p = document.createElement('p'); p.className='no-messages'; p.textContent='아직 메시지가 없습니다.'; msgsDiv.append(p);
    }
    const inputContainer = document.createElement('div'); inputContainer.className='message-input-container';
    const input = document.createElement('input'); input.id='csMessageInput'; input.className='message-input'; input.placeholder='메시지를 입력하세요...';
    const sendBtn = document.createElement('button'); sendBtn.className='send-button'; sendBtn.addEventListener('click', sendCSMessage);
    const icon = document.createElement('i'); icon.className='fas fa-paper-plane'; sendBtn.append(icon, document.createTextNode('전송'));
    inputContainer.append(input, sendBtn);
    container.append(header, msgsDiv, inputContainer);
    csList.append(container);
    msgsDiv.scrollTop = msgsDiv.scrollHeight;

    // WebSocket connect...
    const socket = new SockJS(`${CONFIG_CHAT_BASE}/chat-websocket?userId=${userId}`);
    stompClient = Stomp.over(socket);
    stompClient.connect({ Authorization: token },
      () => stompClient.subscribe(`/topic/${chatRoom.id}`, msg => showCsMessage(JSON.parse(msg.body))),
      e => { console.error('WS 실패', e); alert('채팅 연결에 실패했습니다.'); }
    );
    input.addEventListener('keypress', e => { if (e.key==='Enter') sendCSMessage(); });
  } catch(e) {
    console.error('loadCS',e);
    alert('CS 채팅방을 불러오는데 실패했습니다.');
  }
}

function showCsMessage(m, container = document.getElementById('cs-messages')) {
  const msgDiv = document.createElement('div'); msgDiv.className='message '+(m.senderId.toString()===localStorage.getItem('userId')?'sent':'received');
  const cdiv = document.createElement('div'); cdiv.className='message-content'; cdiv.textContent=m.content;
  const tdiv = document.createElement('div'); tdiv.className='message-time'; tdiv.textContent=new Date().toLocaleString('ko-KR',{hour:'2-digit',minute:'2-digit'});
  const st = document.createElement('span'); st.className='message-status'; st.textContent='✓';
  tdiv.append(st); msgDiv.append(cdiv,tdiv); container.append(msgDiv);
  container.scrollTop = container.scrollHeight;
}

function sendCSMessage() {
  const inp = document.getElementById('csMessageInput'); const txt = inp.value.trim();
  if (!txt || !currentCsRoomId || !stompClient) return;
  const data = { id:Date.now().toString(), content:txt, senderId:localStorage.getItem('userId'), sendedId:userId, chatRoomId:currentCsRoomId, createdAt:new Date().toISOString() };
  stompClient.send(`/app/chat.send/${currentCsRoomId}`, { Authorization: token }, JSON.stringify(data));
  inp.value = '';
}

// ———————————————————————————————————————
// Utilities
// ———————————————————————————————————————
function goBack() { window.location.href = 'admin-dashboard.html'; }
function showUserDetail(id) { window.location.href = `user-detail.html?userId=${id}`; }

document.getElementById('searchInput')?.addEventListener('keypress', e => { if(e.key==='Enter') searchUser(e); });

// ———————————————————————————————————————
// Table render for admin likes
// ———————————————————————————————————————
function renderLikeTable(tbodyId, list) {
  const tbody = document.getElementById(tbodyId); tbody.textContent='';
  if (!Array.isArray(list) || !list.length) {
    const tr=document.createElement('tr'); const td=document.createElement('td');
    td.colSpan=3; td.className='text-center'; td.textContent='데이터가 없습니다.'; tr.append(td); tbody.append(tr);
    return;
  }
  list.forEach(u => {
    const tr=document.createElement('tr');
    const td1=document.createElement('td'); td1.textContent=u.userId;
    const td2=document.createElement('td'); td2.className='name-cell';
    const av=document.createElement('span'); av.className='avatar';
    const img=document.createElement('img'); img.src=u.portfolioImageUrl||'default-profile.png'; img.alt=`${u.name} 프로필`;
    av.append(img);
    const link=document.createElement('a'); link.href=`user-detail.html?userId=${u.userId}`; link.textContent=u.name;
    td2.append(av,link);
    const td3=document.createElement('td'); td3.textContent=new Date(u.likedAt).toLocaleString('ko-KR',{year:'numeric',month:'2-digit',day:'2-digit',hour:'2-digit',minute:'2-digit'});
    tr.append(td1,td2,td3); tbody.append(tr);
  });
}

// ———————————————————————————————————————
// Search in table rows
// ———————————————————————————————————————
function filterTableRows(tbodyId, term) {
  term = term.toLowerCase();
  document.querySelectorAll(`#${tbodyId} tr`).forEach(tr => {
    const name = tr.querySelector('td:nth-child(2)').textContent.toLowerCase();
    tr.style.display = name.includes(term) ? '' : 'none';
  });
}
