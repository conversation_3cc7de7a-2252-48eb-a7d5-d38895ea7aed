package org.kiru.gateway;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/popups")
public class PopupController {
    @GetMapping
    public List<Map<String, String>> getPopups() {
        return List.of(
            Map.of(
                "siteUrl", "https://acre-spool-teal.figma.site",
                "imageUrl", "https://d1l4m6pzgl3p9g.cloudfront.net/popup/CT_poster_%E1%84%8E%E1%85%AC%E1%84%8C%E1%85%A9%E1%86%BC%E1%84%8B%E1%85%A1%E1%86%AB_%E1%84%8B%E1%85%A7%E1%86%BC%E1%84%86%E1%85%AE%E1%86%AB-05+(1).jpg"
            )
        );
    }
} 