plugins {
	id 'java'
	id 'org.springframework.boot' version '3.3.4'
	id 'io.spring.dependency-management' version '1.1.6'
}

bootJar.enabled = false // 빌드시 현재 모듈(multi-module)의 .jar를 생성하지 않습니다.
repositories {
	mavenCentral()
}

subprojects {
	apply plugin: 'java'
	apply plugin: 'idea'
	apply plugin: 'io.spring.dependency-management'
	apply plugin: 'org.springframework.boot'

	group = 'org.kiru'
	version = '0.0.1-SNAPSHOT'

	java {
		toolchain {
			languageVersion = JavaLanguageVersion.of(21)
		}
	}

	configurations {
		compileOnly {
			extendsFrom annotationProcessor
		}
	}

	repositories {
		mavenCentral()
	}
}

project(':alarm-service') {
	dependencies {
		implementation project(':core')
	}
}

project(':chat-service') {
	dependencies {
		implementation project(':core')
	}
}

project(':gateway-service') {
	dependencies {
		implementation project(':core')
	}
}

project(':user-service') {
	dependencies {
		implementation project(':core')
	}
}

processResources{
	filteringCharset 'UTF-8'
}
