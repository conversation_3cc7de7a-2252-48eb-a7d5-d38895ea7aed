package org.kiru.chat.adapter.in.web;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kiru.chat.adapter.out.persistence.MessageRepository;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/internal/chat")
public class ChatInternalController {
    
    private final MessageRepository messageRepository;
    
    @GetMapping("/users/{userId}/messages/yesterday-count")
    public ResponseEntity<Integer> getYesterdayMessageCount(@PathVariable("userId") Long userId) {
        log.info("어제 받은 메시지 수 요청 - userId: {}", userId);
        
        LocalDate yesterday = LocalDate.now().minusDays(1);
        int messageCount = messageRepository.countMessagesReceivedYesterday(userId, yesterday);
        
        log.info("어제 받은 메시지 수 응답 - userId: {}, count: {}", userId, messageCount);
        return ResponseEntity.ok(messageCount);
    }
}
