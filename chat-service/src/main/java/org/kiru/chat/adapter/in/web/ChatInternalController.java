package org.kiru.chat.adapter.in.web;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kiru.chat.adapter.out.persistence.MessageRepository;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/internal/chat")
public class ChatInternalController {

    private final MessageRepository messageRepository;

    @GetMapping("/users/{userId}/messages/today-count")
    public ResponseEntity<Integer> getTodayMessageCount(@PathVariable("userId") Long userId) {
        log.info("당일 받은 메시지 수 요청 - userId: {}", userId);

        LocalDate today = LocalDate.now();
        java.time.LocalDateTime startTime = today.atStartOfDay(); // 00:00:00
        java.time.LocalDateTime endTime = today.atTime(20, 0); // 20:00:00

        int messageCount = messageRepository.countMessagesReceivedToday(userId, startTime, endTime);

        log.info("당일 받은 메시지 수 응답 - userId: {}, count: {}, 기간: {} ~ {}",
            userId, messageCount, startTime, endTime);
        return ResponseEntity.ok(messageCount);
    }
}
