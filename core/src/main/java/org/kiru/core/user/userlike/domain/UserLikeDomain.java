package org.kiru.core.user.userlike.domain;


import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
public class UserLikeDomain implements UserLike{
  private Long userId;
  private Long likedUserId;
  private LikeStatus likeStatus;
  private boolean isMatched;
  private LocalDateTime createdAt;
  private LocalDateTime updatedAt;

  @Builder
  private UserLikeDomain(Long userId, Long likedUserId, LikeStatus likeStatus, boolean isMatched, LocalDateTime createdAt, LocalDateTime updatedAt){
    this.userId = userId;
    this.likedUserId = likedUserId;
    this.likeStatus = likeStatus;
    this.isMatched = isMatched;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }

  public void likeStatus(LikeStatus likeStatus) {
    this.likeStatus = likeStatus;
  }

  public void setMatched(boolean likeOrDislike) {
    this.isMatched = likeOrDislike;
  }

  public void setUpdatedAt() {
    this.updatedAt = LocalDateTime.now();
  }
}
