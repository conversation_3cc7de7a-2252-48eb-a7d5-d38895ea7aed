package org.kiru.user.userlike.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.contains;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.concurrent.Executor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.kiru.core.exception.ConflictException;
import org.kiru.core.exception.code.FailureCode;
import org.kiru.core.user.userlike.domain.LikeStatus;
import org.kiru.core.user.userlike.domain.UserLike;
import org.kiru.user.common.RedissonLockService;
import org.kiru.user.portfolio.dto.res.UserPortfolioResDto;
import org.kiru.user.user.api.ChatApiClient;
import org.kiru.user.userlike.api.CreateChatRoomRequest;
import org.kiru.user.userlike.api.CreateChatRoomResponse;
import org.kiru.user.userlike.dto.res.LikeResponse;
import org.kiru.user.userlike.service.out.GetMatchedUserPortfolioQuery;
import org.kiru.user.userlike.service.out.SendLikeOrDislikeUseCase;

class UserLikeServiceTest {

  private UserLikeService userLikeService;
  private SendLikeOrDislikeUseCase sendLikeOrDislikeUseCase;
  private GetMatchedUserPortfolioQuery getMatchedUserPortfolioQuery;
  private ChatApiClient chatApiClient;
  private MatchNotificationService matchNotificationService;
  private RedissonLockService redissonLockService;
  private Executor executor;
  private UserLike likeResultMock;
  private final Long userId = 1L;
  private final Long likedUserId = 2L;

  @BeforeEach
  void commonSetup() {
    setUp();
    when(redissonLockService.tryAcquireLock(anyString())).thenReturn(true);
    when(sendLikeOrDislikeUseCase.sendLikeOrDislike(eq(userId), eq(likedUserId), eq(LikeStatus.LIKE)))
        .thenReturn(likeResultMock);
    when(likeResultMock.isMatched()).thenReturn(true);

    List<UserPortfolioResDto> portfolios = List.of(mock(UserPortfolioResDto.class));
    when(getMatchedUserPortfolioQuery.findByUserIds(anyList())).thenReturn(portfolios);

    CreateChatRoomResponse chatRoomResponse = new CreateChatRoomResponse(123L);
    when(chatApiClient.createRoom(eq(userId), any(CreateChatRoomRequest.class)))
        .thenReturn(chatRoomResponse);
  }

  void setUp(){
    sendLikeOrDislikeUseCase = mock(SendLikeOrDislikeUseCase.class);
    getMatchedUserPortfolioQuery = mock(GetMatchedUserPortfolioQuery.class);
    chatApiClient = mock(ChatApiClient.class);
    matchNotificationService = mock(MatchNotificationService.class);
    redissonLockService = mock(RedissonLockService.class);
    executor = Runnable::run; // 비동기 테스트용으로 직접 실행
    likeResultMock = mock(UserLike.class);

    userLikeService = new UserLikeService(
        sendLikeOrDislikeUseCase,
        getMatchedUserPortfolioQuery,
        chatApiClient,
        matchNotificationService,
        executor,
        redissonLockService
    );
  }

  @Test
  @DisplayName("매칭 성공 시 LikeResponse(true) 반환")
  void success_whenMatched() {
    // When
    LikeResponse response = userLikeService.sendLikeOrDislike(userId, likedUserId, LikeStatus.LIKE);

    // Then
    assertThat(response.isMatched()).isTrue();
    assertThat(response.getChatRoomId()).isEqualTo(123L);
    assertThat(response.getUserPortfolios()).isNotEmpty();

    verify(redissonLockService).release(anyString());
  }

  @Test
  @DisplayName("매칭 실패 시 false 응답")
  void fail_whenNotMatched() {
    // Given
    when(sendLikeOrDislikeUseCase.sendLikeOrDislike(eq(userId), eq(likedUserId), eq(LikeStatus.DISLIKE)))
        .thenReturn(likeResultMock);
    when(likeResultMock.isMatched()).thenReturn(false);

    // When
    LikeResponse response = userLikeService.sendLikeOrDislike(userId, likedUserId, LikeStatus.DISLIKE);

    // Then
    assertThat(response.isMatched()).isFalse();
    assertThat(response.getChatRoomId()).isNull();
    assertThat(response.getUserPortfolios()).isNull();

    verify(redissonLockService).release(anyString());
  }

  @Test
  @DisplayName("락 획득 실패 시 ConflictException 발생")
  void exception_whenLockFails() {
    // Given
    when(redissonLockService.tryAcquireLock(anyString())).thenReturn(false);

    // When & Then
    assertThatThrownBy(() -> userLikeService.sendLikeOrDislike(userId, likedUserId, LikeStatus.LIKE))
        .isInstanceOf(ConflictException.class)
        .hasFieldOrPropertyWithValue("failureCode", FailureCode.DUPLICATE_LOCK);

    verify(redissonLockService, never()).release(anyString());
  }

  @Test
  @DisplayName("락 획득 후 반드시 release 호출됨")
  void lockAlwaysReleased() {
    // When
    userLikeService.sendLikeOrDislike(userId, likedUserId, LikeStatus.LIKE);

    // Then
    verify(redissonLockService).release(contains("lock:userLike:create:"));
  }

  @Test
  @DisplayName("매칭 성공 후 채팅방 생성 중복 방지 확인")
  void preventChatRoomDuplicationWhenMatchedConcurrently() {
    // When
    LikeResponse firstResponse = userLikeService.sendLikeOrDislike(userId, likedUserId, LikeStatus.LIKE);
    LikeResponse secondResponse = userLikeService.sendLikeOrDislike(userId, likedUserId, LikeStatus.LIKE);

    // Then
    assertThat(firstResponse.isMatched()).isTrue();
    assertThat(firstResponse.getChatRoomId()).isEqualTo(123L);

    assertThat(secondResponse.isMatched()).isTrue();
    assertThat(secondResponse.getChatRoomId()).isEqualTo(123L);

    verify(chatApiClient, times(2)).createRoom(eq(userId), any(CreateChatRoomRequest.class));
    verify(redissonLockService, times(2)).release(anyString());
  }
}