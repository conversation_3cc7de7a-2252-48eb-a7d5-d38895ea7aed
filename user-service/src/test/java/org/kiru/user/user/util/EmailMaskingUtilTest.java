package org.kiru.user.user.util;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.kiru.core.exception.InvalidValueException;

@DisplayName("EmailMaskingUtil 단위 테스트")
class EmailMaskingUtilTest {

  /*
  local part 길이 2~4
  * */
  @Test
  @DisplayName("Given local part length 2, When maskEmail, Then mask = a*")
  void maskLocalPart_length2() {
    // given
    String email = "<EMAIL>";
    // when
    String masked = EmailMaskingUtil.maskEmail(email);
    // then
    assertEquals("a*@e*****e.com", masked);
  }

  @Test
  @DisplayName("Given local part length 3, When maskEmail, Then mask = a*c")
  void maskLocalPart_length3() {
    // given
    String email = "<EMAIL>";
    // when
    String masked = EmailMaskingUtil.maskEmail(email);
    // then
    assertEquals("a*c@e*****e.com", masked);
  }

  @Test
  @DisplayName("Given local part length 4, When maskEmail, Then mask = a*d")
  void maskLocalPart_length4() {
    // given
    String email = "<EMAIL>";
    // when
    String masked = EmailMaskingUtil.maskEmail(email);
    // then
    assertEquals("a*d@e*****e.com", masked);
  }

  @Test
  @DisplayName("Given local part length >4, When maskEmail, Then mask = ab**ef")
  void maskLocalPart_lengthGreaterThan4() {
    // given
    String email = "<EMAIL>";
    // when
    String masked = EmailMaskingUtil.maskEmail(email);
    // then
    assertEquals("ab**ef@e*****e.com", masked);
  }


  /*
  local part 에 특수문자 포함
  * */
  @Test
  @DisplayName("Given local part contains dot/plus, When maskEmail, Then special chars preserved")
  void maskLocalPart_withSpecialChars() {
    // given
    String email = "<EMAIL>";
    // when
    String masked = EmailMaskingUtil.maskEmail(email);
    // then
    // length=5 => first two "a.", one "*" mask, last two "+1"
    assertEquals("a.*+1@e*****e.com", masked);
  }

  /*
  domain part masking (서브도메인 포함)
  * */
  @Test
  @DisplayName("Given domain with subdomain, When maskEmail, Then only 첫 레이블 마스킹")
  void maskDomainPart_withSubdomain() {
    // given
    String email = "<EMAIL>";
    // when
    String masked = EmailMaskingUtil.maskEmail(email);
    // then
    // mail -> m**l, remainder ".example.co.kr"
    assertEquals("u*r@m**l.example.co.kr", masked);
  }

  @Test
  @DisplayName("Given simple domain, When maskEmail, Then mask = e*****e.com")
  void maskDomainPart_simple() {
    // given
    String email = "<EMAIL>";
    // when
    String masked = EmailMaskingUtil.maskEmail(email);
    // then
    assertEquals("x*z@e*****e.com", masked);
  }

  /*
  예외 상황
  * */
  @Test
  @DisplayName("Given missing '@', When maskEmail, Then InvalidValueException")
  void maskEmail_missingAt_throws() {
    // given
    String email = "invalid.email.com";
    // when/then
    assertThrows(InvalidValueException.class, () -> EmailMaskingUtil.maskEmail(email));
  }

  @Test
  @DisplayName("Given domain missing dot, When maskEmail, Then InvalidValueException")
  void maskEmail_domainNoDot_throws() {
    // given
    String email = "user@invaliddomain";
    // when/then
    assertThrows(InvalidValueException.class, () -> EmailMaskingUtil.maskEmail(email));
  }

  @Test
  @DisplayName("Given local part length <2, When maskEmail, Then InvalidValueException")
  void maskEmail_localTooShort_throws() {
    // given
    String email = "<EMAIL>";
    // when/then
    assertThrows(InvalidValueException.class, () -> EmailMaskingUtil.maskEmail(email));
  }

  @Test
  @DisplayName("Given extremely long local part, When maskEmail, Then no overflow and mask succeeds")
  void maskEmail_longLocalPart_noOverflow() {
    // given
    StringBuilder sb = new StringBuilder();
    for (int i = 0; i < 1000; i++) {
      sb.append('x');
    }
    String local = sb.toString();
    String email = local + "@example.com";
    // when
    String masked = EmailMaskingUtil.maskEmail(email);
    // then
    assertTrue(masked.startsWith("xx"), "prefix preserved");
    assertTrue(masked.endsWith("xx@e*****e.com".substring("@".length())), "suffix preserved");
    int stars = masked.substring(2, masked.indexOf("@") - 2).length();
    assertEquals(996, stars, "middle mask length = local.length() - 4");
  }
}