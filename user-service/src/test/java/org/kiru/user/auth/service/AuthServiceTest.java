package org.kiru.user.auth.service;

import static org.assertj.core.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.kiru.core.exception.EntityNotFoundException;
import org.kiru.core.exception.UnauthorizedException;
import org.kiru.core.jwt.AccessTokenGenerator;
import org.kiru.core.jwt.JwtTokenParser;
import org.kiru.core.user.talent.domain.Talent.TalentType;
import org.kiru.core.user.user.domain.LoginType;
import org.kiru.core.user.user.domain.Nationality;
import org.kiru.core.user.user.entity.UserJpaEntity;
import org.kiru.core.user.userPurpose.domain.PurposeType;
import org.kiru.user.auth.jwt.JwtProvider;
import org.kiru.user.auth.jwt.Token;
import org.kiru.user.auth.jwt.redis.RedisTokenService;
import org.kiru.user.user.api.AlarmApiClient;
import org.kiru.user.user.dto.event.UserCreateEvent;
import org.kiru.user.user.dto.request.SignHelpDto;
import org.kiru.user.user.dto.request.UserPurposesReq;
import org.kiru.user.user.dto.request.UserSignInReq;
import org.kiru.user.user.dto.request.UserSignUpReq;
import org.kiru.user.user.dto.request.UserTalentsReq;
import org.kiru.user.user.dto.response.SignHelpDtoRes;
import org.kiru.user.user.dto.response.UserJwtInfoRes;
import org.kiru.user.user.repository.UserRepository;
import org.kiru.user.user.service.AuthService;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.multipart.MultipartFile;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.ExpiredJwtException;

@ExtendWith(MockitoExtension.class)
class AuthServiceTest {

    @InjectMocks
    private AuthService authService;

    @Mock
    private UserRepository userRepository;

    @Mock
    private JwtProvider jwtProvider;

    @Mock
    private RedisTokenService redisTokenService;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private ApplicationEventPublisher applicationEventPublisher;

    @Mock
    private AlarmApiClient alarmApiClient;

    @Mock
    private JwtTokenParser jwtTokenParser;

    @Mock
    private AccessTokenGenerator accessTokenGenerator;

    private UserSignUpReq testSignUpReq;
    private UserJpaEntity testUser;
    private List<UserTalentsReq> talentTypeRequest;
    private List<UserPurposesReq> purposeTypeRequest;
    private Token testToken;
    private List<MultipartFile> images;

    @BeforeEach
    void setUp() {
        testSignUpReq = new UserSignUpReq(
            "<EMAIL>",
            "password123",
            "password123",
            Nationality.KR,
            "Test Description",
            "testuser",
            "http://example.com",
            LoginType.LOCAL,
            "firebaseToken",
            "deviceId",
            "deviceType"
        );

        testUser = UserJpaEntity.builder()
            .id(1L)
            .email("<EMAIL>")
            .password("encodedPassword")
            .username("Test User")
            .nationality(Nationality.KR)
            .loginType(LoginType.LOCAL)
            .build();

        testToken = new Token("testAccessToken", "testRefreshToken");

        talentTypeRequest = List.of(new UserTalentsReq(TalentType.ARCHITECTURE), new UserTalentsReq(TalentType.ADVERTISING));
        purposeTypeRequest = List.of(new UserPurposesReq(PurposeType.ART_RESIDENCY), new UserPurposesReq(PurposeType.GROUP_EXHIBITION));
        images = List.of(
                new MockMultipartFile("image1", "image1.jpg", "image/jpeg", new byte[0]),
                new MockMultipartFile("image2", "image2.jpg", "image/jpeg", new byte[0]),
                new MockMultipartFile("image3", "image3.jpg", "image/jpeg", new byte[0])
        )
        ;
    }

    @Test
    @DisplayName("회원가입 - 성공")
    void signUp_Success() {
        // Given
        when(passwordEncoder.encode(anyString())).thenReturn("encodedPassword");
        when(userRepository.save(any(UserJpaEntity.class))).thenReturn(testUser);
        when(jwtProvider.issueToken(anyLong(), anyString(), any(Date.class))).thenReturn(testToken);
        doNothing().when(redisTokenService).saveRefreshToken(anyLong(), anyString());

        // When
        UserJwtInfoRes result = authService.signUp(testSignUpReq, images, purposeTypeRequest, talentTypeRequest);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.accessToken()).isNotNull();
        verify(userRepository).save(any(UserJpaEntity.class));
        verify(redisTokenService).saveRefreshToken(anyLong(), anyString());
        // publishEvent 호출 검증
        ArgumentCaptor<UserCreateEvent> eventCaptor = ArgumentCaptor.forClass(UserCreateEvent.class);
        verify(applicationEventPublisher, times(1)).publishEvent(eventCaptor.capture());

        UserCreateEvent capturedEvent = eventCaptor.getValue();
        assertThat(capturedEvent).isNotNull();
        assertThat(capturedEvent.userId()).isEqualTo(testUser.getId());
        assertThat(capturedEvent.userName()).isEqualTo(testUser.getUsername());
        assertThat(capturedEvent.images()).isEqualTo(images);
        assertThat(capturedEvent.purposes()).isEqualTo(purposeTypeRequest);
        assertThat(capturedEvent.talents()).isEqualTo(talentTypeRequest);
    }

    @Test
    @DisplayName("로그인 - 성공")
    void signIn_Success() {
        // Given
        UserSignInReq signInReq = new UserSignInReq("<EMAIL>", "password123", "firebaseToken", "deviceId", "deviceType");
        when(userRepository.findByEmail(eq(signInReq.email()))).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches(anyString(), anyString())).thenReturn(true);
        when(jwtProvider.issueToken(anyLong(), anyString(), any(Date.class))).thenReturn(testToken);
        doNothing().when(redisTokenService).deleteRefreshToken(anyLong());
        doNothing().when(redisTokenService).saveRefreshToken(anyLong(), anyString());

        // When
        UserJwtInfoRes result = authService.signIn(signInReq);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.accessToken()).isEqualTo("testAccessToken");
        verify(userRepository).findByEmail(eq(signInReq.email()));
        verify(redisTokenService).deleteRefreshToken(anyLong());
        verify(redisTokenService).saveRefreshToken(anyLong(), anyString());
    }

    @Test
    @DisplayName("로그인 - 잘못된 비밀번호")
    void signIn_InvalidPassword() {
        // Given
        UserSignInReq signInReq = new UserSignInReq("<EMAIL>", "wrongPassword", "firebaseToken", "deviceId", "deviceType");
        when(userRepository.findByEmail(signInReq.email())).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches(any(), any())).thenReturn(false);

        // When & Then
        assertThrows(UnauthorizedException.class, () -> authService.signIn(signInReq));
    }

    @Test
    @DisplayName("토큰 재발급 - 성공")
    void reissue_Success() {
        // Given
        String refreshToken = "testRefreshToken";
        Long userId = 1L;
        String email = "<EMAIL>";
        
        Jws<Claims> mockJws = mock(Jws.class);
        Claims mockClaims = mock(Claims.class);

        when(jwtTokenParser.parseToken(refreshToken)).thenReturn(mockJws);
        when(jwtTokenParser.getUserIdFromClaims(mockJws)).thenReturn(userId);
        when(jwtTokenParser.getEmailFromClaims(mockJws)).thenReturn(email);
        when(redisTokenService.validateRefreshToken(userId, refreshToken)).thenReturn(true);
        when(accessTokenGenerator.generateToken(eq(userId), eq(email), any(Date.class))).thenReturn("newAccessToken");

        // When
        UserJwtInfoRes result = authService.reissue(refreshToken);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.accessToken()).isEqualTo("newAccessToken");
        assertThat(result.refreshToken()).isEqualTo(refreshToken);
        verify(jwtTokenParser).parseToken(refreshToken);
        verify(jwtTokenParser).getUserIdFromClaims(mockJws);
        verify(jwtTokenParser).getEmailFromClaims(mockJws);
        verify(redisTokenService).validateRefreshToken(userId, refreshToken);
        verify(accessTokenGenerator).generateToken(eq(userId), eq(email), any(Date.class));
    }

    @Test
    @DisplayName("토큰 재발급 - 유효하지 않은 리프레시 토큰")
    void reissue_InvalidRefreshToken() {
        // Given
        String refreshToken = "invalidRefreshToken";
        Long userId = 1L;
        String email = "<EMAIL>";

        Jws<Claims> mockJws = mock(Jws.class);

// parser → mockJws
        when(jwtTokenParser.parseToken(refreshToken)).thenReturn(mockJws);
// parser 에서 userId, email 꺼낼 때
        when(jwtTokenParser.getUserIdFromClaims(mockJws)).thenReturn(userId);
        when(jwtTokenParser.getEmailFromClaims(mockJws)).thenReturn(email);

// 이 부분은 검증로직만 테스트하니 계속 false
        when(redisTokenService.validateRefreshToken(any(), any())).thenReturn(false);

// When & Then
        assertThrows(UnauthorizedException.class, () -> authService.reissue(refreshToken));

        verify(jwtTokenParser).parseToken(refreshToken);
        verify(jwtTokenParser).getUserIdFromClaims(mockJws);
        verify(jwtTokenParser).getEmailFromClaims(mockJws);
// 이제 1L, "invalidRefreshToken" 으로 호출됩니다!
        verify(redisTokenService).validateRefreshToken(eq(userId), eq(refreshToken));
        verify(redisTokenService).validateRefreshToken(eq(userId), eq(refreshToken));
    }

    @Test
    @DisplayName("토큰 재발급 - 만료된 리프레시 토큰")
    void reissue_ExpiredRefreshToken() {
        // Given
        String refreshToken = "expiredRefreshToken";
        when(jwtTokenParser.parseToken(refreshToken)).thenThrow(new ExpiredJwtException(null, null, "Token expired"));

        // When & Then
        assertThrows(UnauthorizedException.class, () -> authService.reissue(refreshToken));
        verify(jwtTokenParser).parseToken(refreshToken);
    }

    @Test
    @DisplayName("로그아웃 - 성공")
    void logout_Success() {
        // Given
        Long userId = 1L;
        String deviceId = "testDeviceId";
        doNothing().when(redisTokenService).deleteRefreshToken(eq(userId));
        when(alarmApiClient.deleteDevice(eq(userId), eq(deviceId))).thenReturn(deviceId);

        // When
        authService.logout(userId, deviceId);

        // Then
        verify(redisTokenService).deleteRefreshToken(eq(userId));
        verify(alarmApiClient).deleteDevice(eq(userId), eq(deviceId));
    }

    @Test
    @DisplayName("아이디 찾기 - 성공")
    void signHelp_Success() {
        // Given
        SignHelpDto signHelpDto = new SignHelpDto("Test User");
        when(userRepository.findByUsername(signHelpDto.userName())).thenReturn(Optional.of("<EMAIL>"));

        // When
        SignHelpDtoRes result = authService.signHelp(signHelpDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getDecodeEmail()).contains("*");
        verify(userRepository).findByUsername(signHelpDto.userName());
    }

    @Test
    @DisplayName("아이디 찾기 - 존재하지 않는 사용자")
    void signHelp_UserNotFound() {
        // Given
        SignHelpDto signHelpDto = new SignHelpDto("NonExistentUser");
        when(userRepository.findByUsername(signHelpDto.userName())).thenReturn(Optional.empty());

        // When & Then
        assertThrows(EntityNotFoundException.class, () -> authService.signHelp(signHelpDto));
    }
}
