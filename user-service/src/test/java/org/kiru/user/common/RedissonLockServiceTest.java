package org.kiru.user.common;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

class RedissonLockServiceTest {

  private RedissonClient redissonClient;
  private RedissonLockService redissonLockService;
  private RLock lock;

  @BeforeEach
  void setUp() {
    redissonClient = mock(RedissonClient.class);
    redissonLockService = new RedissonLockService(redissonClient);
    lock = mock(RLock.class);

    when(redissonClient.getLock(anyString())).thenReturn(lock);
  }

  @Test
  @DisplayName("락 획득 성공 시 true 반환")
  void should_return_true_when_lock_acquired() throws Exception {
    // Given
    when(lock.tryLock(4, 3, TimeUnit.SECONDS)).thenReturn(true);

    // When
    boolean result = redissonLockService.tryAcquireLock("test:lock:key");

    // Then
    assertThat(result).isTrue();
    verify(lock).tryLock(4, 3, TimeUnit.SECONDS);
  }


  @Test
  @DisplayName("락 획득 중 InterruptedException 발생 시 false 반환")
  void should_return_false_when_interrupted() throws Exception {
    // Given
    when(lock.tryLock(4, 3, TimeUnit.SECONDS)).thenThrow(new InterruptedException());

    // When
    boolean result = redissonLockService.tryAcquireLock("test:lock:key");

    // Then
    assertThat(result).isFalse();
  }

  @Test
  @DisplayName("현재 쓰레드가 락을 보유 중이면 unlock() 호출")
  void should_unlock_when_lock_is_held_by_current_thread() {
    // Given
    when(lock.isHeldByCurrentThread()).thenReturn(true);

    // When
    redissonLockService.release("test:lock:key");

    // Then
    verify(lock).unlock();
  }

  @Test
  @DisplayName("현재 쓰레드가 락을 보유하지 않으면 unlock() 호출 안함")
  void should_not_unlock_when_lock_not_held() {
    // Given
    when(lock.isHeldByCurrentThread()).thenReturn(false);

    // When
    redissonLockService.release("test:lock:key");

    // Then
    verify(lock, never()).unlock();
  }
}