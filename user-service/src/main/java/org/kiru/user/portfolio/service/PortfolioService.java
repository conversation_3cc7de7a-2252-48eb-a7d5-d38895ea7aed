package org.kiru.user.portfolio.service;

import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kiru.core.exception.EntityNotFoundException;
import org.kiru.core.exception.code.FailureCode;
import org.kiru.core.user.user.domain.User;
import org.kiru.user.portfolio.dto.res.UserPortfolioResDto;
import org.kiru.user.portfolio.service.out.GetRecommendUserIdsQuery;
import org.kiru.user.portfolio.service.out.GetUserPortfoliosQuery;
import org.kiru.user.user.service.UserService;
import org.kiru.user.userBlock.service.out.BlockUserQuery;
import org.kiru.user.userBlock.service.out.GetUserBlockQuery;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class PortfolioService {
    private final GetUserPortfoliosQuery getUserPortfoliosQuery;
    private final GetRecommendUserIdsQuery getRecommendUserIdsQuery;
    private final GetUserBlockQuery getUserBlockQuery;
    private final UserService userService;

    public List<UserPortfolioResDto> getUserPortfolios(Long userId, Pageable pageable) {
        List<Long> recommendedUserIds = getRecommendUserIdsQuery.findRecommendedUserIds(userId, pageable);
        log.info("최종 추천한 유저 Id: {}, 개수: {}", recommendedUserIds, recommendedUserIds.size());
        return getUserPortfoliosQuery.findAllPortfoliosByUserIds(recommendedUserIds);
    }

    public User getOtherPortfolio(Long userId, Long portfolioUserId) {
        // portfolioUserId가 userId를 차단한 경우에는 접근 불가
        if(getUserBlockQuery.hasBlockedUser(portfolioUserId, userId)) {
            throw new EntityNotFoundException(FailureCode.USER_NOT_FOUND);
        }
        return userService.getUserFromIdToMainPage(portfolioUserId);
    }
}
