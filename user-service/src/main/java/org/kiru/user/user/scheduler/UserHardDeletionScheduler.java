package org.kiru.user.user.scheduler;

import java.time.LocalDateTime;
import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kiru.user.user.repository.UserRepository;
import org.kiru.user.user.service.UserService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class UserHardDeletionScheduler {

    private final UserRepository userRepository;
    private final UserService userService;

    // 매일 자정에 실행되는 작업으로, 탈퇴한지 7일이 지난 유저들을 완전히 삭제합니다.
    @Scheduled(cron = "0 0 0 * * *")
    public void deleteUsersAfterSevenDays() {
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
        log.info("Searching for users hard deleted before {}", sevenDaysAgo);

        List<Long> userIdsToDelete = userRepository.findUserIdsByDeletedIsTrueAndDeletedAtBefore(sevenDaysAgo);
        log.info("Found {} users to hard delete", userIdsToDelete.size());
        for (Long userId : userIdsToDelete) {
            try {
                userService.hardDeleteUser(userId);
            } catch (Exception e) {
                log.error("Failed to hard delete user: Id={}, Error: {}", userId, e.getMessage(), e);
            }
        }
        log.info("User hard deletion job completed. Successfully deleted {} users", userIdsToDelete.size());
    }
} 