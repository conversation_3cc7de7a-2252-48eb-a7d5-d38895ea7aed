package org.kiru.user.user.controller;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.kiru.user.auth.argumentresolve.UserId;
import org.kiru.user.user.dto.request.*;
import org.kiru.user.user.dto.request.validation.*;
import org.kiru.user.user.dto.response.SignHelpDtoRes;
import org.kiru.user.user.dto.response.UserJwtInfoRes;
import org.kiru.user.user.service.AuthService;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/users")
public class UserLoginController {
    private final AuthService authService;

    @PostMapping(value = "/signup", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE,
            MediaType.APPLICATION_JSON_VALUE}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<UserJwtInfoRes> signUp(
            @RequestPart("userSignUpReq") @Valid final UserSignUpReq userSignUpReq,
            @RequestPart("portfolioImgs") @Valid @Size(max = 9) final List<MultipartFile> images,
            @RequestPart("purpose") @Valid @NotEmpty final List<UserPurposesReq> purposes,
            @RequestPart("talent") @Valid @NotEmpty final List<UserTalentsReq> talents
    ) {
        UserJwtInfoRes userSignUpRes = authService.signUp(userSignUpReq, images, purposes, talents);
        return ResponseEntity.status(HttpStatus.CREATED).body(userSignUpRes);
    }

    @PostMapping("/signin")
    public ResponseEntity<UserJwtInfoRes> signIn(
            @RequestBody final UserSignInReq userSignInReq
    ) {
        UserJwtInfoRes userSignInRes = authService.signIn(userSignInReq);

        return ResponseEntity.status(HttpStatus.CREATED).body(userSignInRes);
    }

    @PostMapping("/signin/admin")
    public ResponseEntity<UserJwtInfoRes> signInAdmin(
            @RequestBody final UserSignInReq userSignInReq
    ) {
        UserJwtInfoRes userSignInRes = authService.signInAdmin(userSignInReq);

        return ResponseEntity.status(HttpStatus.CREATED).body(userSignInRes);
    }

    @PostMapping("/signin/help") // 이 부분은 각자 바꿔주시면 됩니다.
    public ResponseEntity<SignHelpDtoRes> signHelp(@RequestBody SignHelpDto signHelpDto) {
        return ResponseEntity.ok(authService.signHelp(signHelpDto));
    }

    @PostMapping("/logout")
    public ResponseEntity<Void> logout(
            @UserId Long userId,
            @RequestBody final LogoutReq logoutReq
    ) {
        authService.logout(userId, logoutReq.deviceId());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/validate/email")
    public void validateEmail(@RequestBody @Valid EmailValidationReq emailReq) { }

    @PostMapping("/validate/password")
    public void validatePassword(@RequestBody @Valid PasswordValidationReq passwordReq) { }

    @PostMapping("/validate/name")
    public void validateName(@RequestBody @Valid NameValidationReq nameReq) { }

    @PostMapping("/validate/nationality")
    public void validateNationality(@RequestBody @Valid NationalityValidationReq nationalityReq) { }

    @PostMapping("/validate/webUrl")
    public void validateWebUrl(@RequestBody @Valid WebUrlValidationReq webUrlReq) { }

    @PostMapping("/validate/description")
    public void validateDescription(@RequestBody @Valid DescriptionValidationReq descriptionReq) { }

    @PostMapping("/validate/instagramId")
    public void validateInstagramId(@RequestBody @Valid InstagramIdValidationReq instagramIdReq) { }

    @PostMapping("/validate/loginType")
    public void validateLoginType(@RequestBody @Valid LoginTypeValidationReq loginTypeReq) { }

    @PostMapping("/validate/firebaseToken")
    public void validateFirebaseToken(@RequestBody @Valid FirebaseTokenValidationReq firebaseTokenReq) { }

    @PostMapping("/validate/deviceId")
    public void validateDeviceId(@RequestBody @Valid DeviceIdValidationReq deviceIdReq) { }

    @PostMapping("/validate/deviceType")
    public void validateDeviceType(@RequestBody @Valid DeviceTypeValidationReq deviceTypeReq) { }
}