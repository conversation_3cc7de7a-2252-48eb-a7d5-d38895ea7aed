package org.kiru.user.user.util;

import org.kiru.core.exception.InvalidValueException;
import org.kiru.core.exception.code.FailureCode;

public class EmailMaskingUtil {
    private static final String MASK_CHAR = "*";
    private static final String DOMAIN_SUFFIX = ".***";

    public static String maskEmail(String email) {
        try {
            int atIndex = email.indexOf("@");
            if (atIndex <= 0) {
                throw new IllegalArgumentException("Invalid email address");
            }
            String localPart = email.substring(0, atIndex);
            String maskedLocalPart = maskLocalPart(localPart);

            String domainPart = email.substring(atIndex + 1);
            String maskedDomainPart = maskDomainPart(domainPart);

            return maskedLocalPart + "@" + maskedDomainPart;
        } catch (StringIndexOutOfBoundsException e){
            throw new InvalidValueException(FailureCode.INVALID_EMAIL_FORMAT);
        } catch (IllegalArgumentException e) {
            throw new InvalidValueException(FailureCode.INVALID_EMAIL_FORMAT);
        }
    }

    private static String maskLocalPart(String localPart) {
        int length = localPart.length();
        if (length == 2) {
            return "" + localPart.charAt(0) + MASK_CHAR;
        } else if (length == 3) {
            return "" + localPart.charAt(0) + MASK_CHAR + localPart.charAt(2);
        } else if (length == 4) {
            return localPart.charAt(0) + MASK_CHAR + localPart.charAt(length - 1);
        } else {
            String middleMask = MASK_CHAR.repeat(Math.max(0, length - 4));
            return "" + localPart.charAt(0) + localPart.charAt(1) + middleMask
                    + localPart.charAt(length - 2) + localPart.charAt(length - 1);
        }
    }
    private static String maskDomainPart(String domainPart) {
        int dotIndex = domainPart.indexOf('.');
        if (dotIndex < 1 || dotIndex == domainPart.length() - 1) {
            throw new IllegalArgumentException("Invalid domain format: " + domainPart);
        }

        String label    = domainPart.substring(0, dotIndex);
        String remainder = domainPart.substring(dotIndex);

        if (label.length() < 2) {
            throw new IllegalArgumentException("Invalid domain label: " + label);
        }

        String maskedLabel = new StringBuilder()
            .append(label.charAt(0))
            .append(MASK_CHAR.repeat(label.length() - 2))
            .append(label.charAt(label.length() - 1))
            .toString();

        return maskedLabel + remainder;
    }
} 