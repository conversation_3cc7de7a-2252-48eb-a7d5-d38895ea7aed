package org.kiru.user.user.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kiru.user.user.dto.response.UserActivitySummaryResponse;
import org.kiru.user.user.service.UserActivityService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/internal/users")
public class UserInternalController {

    private final UserActivityService userActivityService;

    @GetMapping("/{userId}/activity-summary")
    public ResponseEntity<UserActivitySummaryResponse> getUserActivitySummary(@PathVariable("userId") Long userId) {
        log.info("사용자 활동 요약 요청 - userId: {}", userId);
        UserActivitySummaryResponse summary = userActivityService.getUserActivitySummary(userId);
        log.info("사용자 활동 요약 응답 - userId: {}, likes: {}, matches: {}, messages: {}",
            userId, summary.likesCount(), summary.matchesCount(), summary.messagesCount());
        return ResponseEntity.ok(summary);
    }

    @GetMapping("/{userId}/activity-summary/today")
    public ResponseEntity<UserActivitySummaryResponse> getTodayActivitySummary(@PathVariable("userId") Long userId) {
        log.info("오늘 사용자 활동 요약 요청 - userId: {}", userId);
        UserActivitySummaryResponse summary = userActivityService.getTodayActivitySummary(userId);
        log.info("오늘 사용자 활동 요약 응답 - userId: {}, likes: {}, matches: {}, messages: {}",
            userId, summary.likesCount(), summary.matchesCount(), summary.messagesCount());
        return ResponseEntity.ok(summary);
    }
}
