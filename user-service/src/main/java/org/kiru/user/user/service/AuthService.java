package org.kiru.user.user.service;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jws;
import java.util.Date;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kiru.core.exception.ConflictException;
import org.kiru.core.exception.EntityNotFoundException;
import org.kiru.core.exception.UnauthorizedException;
import org.kiru.core.exception.code.FailureCode;
import org.kiru.core.jwt.AccessTokenGenerator;
import org.kiru.core.jwt.JwtTokenParser;
import org.kiru.core.user.user.domain.LoginType;
import org.kiru.core.user.user.domain.User;
import org.kiru.core.user.user.entity.UserJpaEntity;
import org.kiru.user.auth.jwt.JwtProvider;
import org.kiru.user.auth.jwt.Token;
import org.kiru.user.auth.jwt.redis.RedisTokenService;
import org.kiru.user.user.api.AlarmApiClient;
import org.kiru.user.user.dto.event.UserCreateEvent;
import org.kiru.user.user.dto.request.CreatedDeviceReq;
import org.kiru.user.user.dto.request.SignHelpDto;
import org.kiru.user.user.dto.request.UserPurposesReq;
import org.kiru.user.user.dto.request.UserSignInReq;
import org.kiru.user.user.dto.request.UserSignUpReq;
import org.kiru.user.user.dto.request.UserTalentsReq;
import org.kiru.user.user.dto.response.SignHelpDtoRes;
import org.kiru.user.user.dto.response.UserJwtInfoRes;
import org.kiru.user.user.repository.UserRepository;
import org.kiru.user.user.util.EmailMaskingUtil;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RequiredArgsConstructor
@Transactional(readOnly = true)
@Service
public class AuthService {

  private final UserRepository userRepository;
  private final JwtProvider jwtProvider;
  private final AlarmApiClient alarmApiClient;
  private final RedisTokenService redisTokenService;
  private final ApplicationEventPublisher applicationEventPublisher;
  private final PasswordEncoder passwordEncoder;
  private final JwtTokenParser jwtTokenParser;
  private final AccessTokenGenerator accessTokenGenerator;

  @Transactional
  public UserJwtInfoRes signUp(UserSignUpReq req, List<MultipartFile> images,
      List<UserPurposesReq> purposes, List<UserTalentsReq> talents) {

    validateUsername(req.name());
    User newUser = createUser(req);
    UserJpaEntity userEntity = userRepository.save(UserJpaEntity.of(newUser));

    Token issuedToken = issueAndSaveToken(userEntity);
    publishUserCreateEvent(userEntity, images, purposes, talents);
    handleFirebaseToken(userEntity.getId(), req);

    return UserJwtInfoRes.of(userEntity.getId(), issuedToken.accessToken(),
        issuedToken.refreshToken());
  }

  private void validateUsername(String username) {
    if (userRepository.findByUsername(username).isPresent()) {
      throw new ConflictException(FailureCode.DUPLICATE_NICKNAME);
    }
  }

  private User createUser(UserSignUpReq req) {
    User.UserBuilder userBuilder = User.builder()
        .description(req.description())
        .email(req.email())
        .instagramId(req.instagramId())
        .loginType(req.loginType())
        .username(req.name())
        .nationality(req.nationality())
        .webUrl(req.webUrl());

    if (req.loginType() == LoginType.LOCAL) {
      userBuilder.password(encodePassword(req.password()));
    }
    return userBuilder.build();
  }

  private void handleFirebaseToken(Long userId, UserSignUpReq req) {
    if (req != null) {
      saveFirebaseToken(userId, req .firebaseToken(), req .deviceType(), req .deviceId());
    }
  }

  private void handleFirebaseToken(Long userId, UserSignInReq signInReq){
    if (signInReq.firebaseToken() != null) {
      saveFirebaseToken(userId, signInReq.firebaseToken(), signInReq.deviceType(), signInReq.deviceId());
    }
  }

  private Token issueAndSaveToken(UserJpaEntity user){
    Date now = new Date();
    redisTokenService.deleteRefreshToken(user.getId());
    Token issuedToken = jwtProvider.issueToken(user.getId(), user.getEmail(), now);
    redisTokenService.saveRefreshToken(user.getId(), issuedToken.refreshToken());
    return issuedToken;
  }

  private void publishUserCreateEvent(UserJpaEntity user, List<MultipartFile> images, List<UserPurposesReq> purposes, List<UserTalentsReq> talents){
    applicationEventPublisher.publishEvent(
        UserCreateEvent.builder()
            .userName(user.getUsername())
            .userId(user.getId())
            .images(images)
            .purposes(purposes)
            .talents(talents)
            .build()
    );
  }

  /**
   * 로그인 처리.
   */
  @Transactional
  public UserJwtInfoRes signIn(final UserSignInReq req) {
    UserJpaEntity user = validateUserCredentials(req);
    Token issuedToken = issueAndSaveToken(user);
    handleFirebaseToken(user.getId(), req);
    return UserJwtInfoRes.of(user.getId(), issuedToken.accessToken(), issuedToken.refreshToken());
  }

  @Transactional
  public UserJwtInfoRes signInAdmin(final UserSignInReq req) {

    UserJpaEntity user = validateUserCredentials(req);
    validateAdminAccess(user.getLoginType());

    Token issuedToken = issueAndSaveToken(user);
    handleFirebaseToken(user.getId(), req);

    return UserJwtInfoRes.of(user.getId(), issuedToken.accessToken(), issuedToken.refreshToken());
  }

  private UserJpaEntity validateUserCredentials(UserSignInReq req) {
    UserJpaEntity user = userRepository.findByEmail(req.email())
        .orElseThrow(() -> new UnauthorizedException(FailureCode.INVALID_USER_CREDENTIALS));

    if (!passwordEncoder.matches(req.password(), user.getPassword())) {
      throw new UnauthorizedException(FailureCode.PASSWORD_MISMATCH);
    }

    return user;
  }

  private void validateAdminAccess(LoginType userType) {
    if (userType != LoginType.ADMIN) {
      throw new UnauthorizedException(FailureCode.UNAUTHORIZED);
    }
  }

  /**
   * 토큰 재발급 처리.
   */
  public UserJwtInfoRes reissue(final String refreshToken) {
    try {
      Jws<Claims> claims = jwtTokenParser.parseToken(refreshToken);
      Long userId = jwtTokenParser.getUserIdFromClaims(claims);
      String email = jwtTokenParser.getEmailFromClaims(claims);

      validateRefreshToken(userId, refreshToken);
      String newAccessToken = accessTokenGenerator.generateToken(userId, email, new Date());

      return UserJwtInfoRes.of(userId, newAccessToken, refreshToken);
    } catch (ExpiredJwtException e) {
      throw new UnauthorizedException(FailureCode.EXPIRED_REFRESH_TOKEN);
    } catch (Exception e) {
      log.error("Token reissue failed", e);
      throw new UnauthorizedException(FailureCode.INVALID_REFRESH_TOKEN_VALUE);
    }
  }

  private void validateRefreshToken(Long userId, String refreshToken) {
    if (!redisTokenService.validateRefreshToken(userId, refreshToken)) {
      throw new UnauthorizedException(FailureCode.INVALID_REFRESH_TOKEN_VALUE);
    }
  }

  private String encodePassword(String rawPassword) {
    return passwordEncoder.encode(rawPassword);
  }

  public boolean checkPassword(String rawPassword, String encodedPassword) {
    if (!passwordEncoder.matches(rawPassword, encodedPassword)) {
      throw new UnauthorizedException(FailureCode.PASSWORD_MISMATCH);
    }
    return true;
  }

  public SignHelpDtoRes signHelp(SignHelpDto signHelpDto) {
    String email = userRepository.findByUsername(signHelpDto.userName())
        .orElseThrow(() -> new EntityNotFoundException(FailureCode.USER_NOT_FOUND));
    String maskedEmail = EmailMaskingUtil.maskEmail(email);
    return new SignHelpDtoRes(maskedEmail);
  }

  private void saveFirebaseToken(Long userId, String firebaseToken, String deviceType,
      String deviceId) {
    CreatedDeviceReq createdDeviceReq = CreatedDeviceReq.of(userId, firebaseToken, deviceType,
        deviceId);
    log.info("Firebase token save request: {}", createdDeviceReq.getDeviceId());
    alarmApiClient.createDevice(createdDeviceReq);
  }

  public void logout(Long userId, String deviceId) {
    redisTokenService.deleteRefreshToken(userId);
    if (deviceId != null) {
      alarmApiClient.deleteDevice(userId, deviceId);
    }
  }
}
