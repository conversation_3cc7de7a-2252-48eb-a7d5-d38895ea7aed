package org.kiru.user.user.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kiru.user.user.api.ChatApiClient;
import org.kiru.user.user.dto.response.UserActivitySummaryResponse;
import org.kiru.user.userlike.adapter.UserLikeJpaAdapter;
import org.kiru.user.userlike.adapter.UserLikeMongoAdapter;
import org.kiru.user.userlike.repository.UserLikeJpaRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserActivityService {

    private final ChatApiClient chatApiClient;

    @Autowired(required = false)
    private UserLikeJpaRepository userLikeJpaRepository;

    @Autowired(required = false)
    private UserLikeMongoAdapter userLikeMongoAdapter;

    public UserActivitySummaryResponse getUserActivitySummary(Long userId) {
        log.info("사용자 활동 요약 계산 시작 - userId: {}", userId);

        LocalDate today = LocalDate.now();
        java.time.LocalDateTime startTime = today.atStartOfDay(); // 00:00:00
        java.time.LocalDateTime endTime = today.atTime(20, 0); // 20:00:00

        try {
            int likesCount = 0;
            int matchesCount = 0;

            // JPA 또는 MongoDB 구현체 중 사용 가능한 것으로 계산
            if (userLikeJpaRepository != null) {
                log.info("JPA Repository를 사용하여 활동 요약 계산 - userId: {}, 기간: {} ~ {}", userId, startTime, endTime);
                likesCount = userLikeJpaRepository.countLikesReceivedToday(userId, startTime, endTime);
                matchesCount = userLikeJpaRepository.countMatchesToday(userId, startTime, endTime);
            } else if (userLikeMongoAdapter != null) {
                log.info("MongoDB Adapter를 사용하여 활동 요약 계산 - userId: {}", userId);
                // MongoDB는 아직 시간 범위 쿼리 미구현
                log.warn("MongoDB는 시간 범위 활동 요약 미지원 - userId: {}", userId);
            } else {
                log.warn("사용 가능한 UserLike Repository가 없음 - userId: {}", userId);
            }

            log.info("당일 받은 좋아요 수 - userId: {}, count: {}", userId, likesCount);
            log.info("당일 새로 매칭된 수 - userId: {}, count: {}", userId, matchesCount);

            // 당일 받은 메시지 수
            int messagesCount = 0;
            try {
                messagesCount = chatApiClient.getTodayMessageCount(userId);
                log.info("당일 받은 메시지 수 - userId: {}, count: {}", userId, messagesCount);
            } catch (Exception e) {
                log.error("메시지 수 조회 실패 - userId: {}, error: {}", userId, e.getMessage());
                // 메시지 수 조회 실패 시 0으로 처리
            }

            UserActivitySummaryResponse response = UserActivitySummaryResponse.of(likesCount, matchesCount, messagesCount);
            log.info("사용자 활동 요약 계산 완료 - userId: {}, response: {}", userId, response);

            return response;

        } catch (Exception e) {
            log.error("사용자 활동 요약 계산 실패 - userId: {}, error: {}", userId, e.getMessage(), e);
            // 실패 시 모든 카운트를 0으로 반환
            return UserActivitySummaryResponse.of(0, 0, 0);
        }
    }

    // 테스트용: 오늘 활동 요약 (테스트 데이터 확인용)
    public UserActivitySummaryResponse getTodayActivitySummary(Long userId) {
        log.info("오늘 사용자 활동 요약 계산 시작 - userId: {}", userId);

        try {
            int likesCount = 0;
            int matchesCount = 0;

            // JPA 또는 MongoDB 구현체 중 사용 가능한 것으로 계산
            if (userLikeJpaRepository != null) {
                log.info("JPA Repository를 사용하여 오늘 전체 활동 요약 계산 - userId: {}", userId);
                likesCount = userLikeJpaRepository.countLikesReceivedTodayAll(userId);
                matchesCount = userLikeJpaRepository.countMatchesTodayAll(userId);
            } else {
                log.warn("MongoDB는 오늘 활동 요약 테스트 미지원 - userId: {}", userId);
            }

            log.info("오늘 받은 좋아요 수 - userId: {}, count: {}", userId, likesCount);
            log.info("오늘 새로 매칭된 수 - userId: {}, count: {}", userId, matchesCount);

            // 오늘 받은 메시지 수는 0으로 처리 (테스트 복잡도 감소)
            int messagesCount = 0;

            UserActivitySummaryResponse response = UserActivitySummaryResponse.of(likesCount, matchesCount, messagesCount);
            log.info("오늘 사용자 활동 요약 계산 완료 - userId: {}, response: {}", userId, response);

            return response;

        } catch (Exception e) {
            log.error("오늘 사용자 활동 요약 계산 실패 - userId: {}, error: {}", userId, e.getMessage(), e);
            return UserActivitySummaryResponse.of(0, 0, 0);
        }
    }
