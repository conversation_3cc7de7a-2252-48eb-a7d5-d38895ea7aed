package org.kiru.user.user.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kiru.user.user.api.ChatApiClient;
import org.kiru.user.user.dto.response.UserActivitySummaryResponse;
import org.kiru.user.userlike.adapter.UserLikeJpaAdapter;
import org.kiru.user.userlike.adapter.UserLikeMongoAdapter;
import org.kiru.user.userlike.repository.UserLikeJpaRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserActivityService {

    private final ChatApiClient chatApiClient;

    @Autowired(required = false)
    private UserLikeJpaRepository userLikeJpaRepository;

    @Autowired(required = false)
    private UserLikeMongoAdapter userLikeMongoAdapter;

    public UserActivitySummaryResponse getUserActivitySummary(Long userId) {
        log.info("사용자 활동 요약 계산 시작 - userId: {}", userId);

        LocalDate yesterday = LocalDate.now().minusDays(1);

        try {
            int likesCount = 0;
            int matchesCount = 0;

            // JPA 또는 MongoDB 구현체 중 사용 가능한 것으로 계산
            if (userLikeJpaRepository != null) {
                log.info("JPA Repository를 사용하여 활동 요약 계산 - userId: {}", userId);
                likesCount = userLikeJpaRepository.countLikesReceivedYesterday(userId, yesterday);
                matchesCount = userLikeJpaRepository.countMatchesYesterday(userId, yesterday);
            } else if (userLikeMongoAdapter != null) {
                log.info("MongoDB Adapter를 사용하여 활동 요약 계산 - userId: {}", userId);
                likesCount = userLikeMongoAdapter.countLikesReceivedYesterday(userId, yesterday);
                matchesCount = userLikeMongoAdapter.countMatchesYesterday(userId, yesterday);
            } else {
                log.warn("사용 가능한 UserLike Repository가 없음 - userId: {}", userId);
            }

            log.info("어제 받은 좋아요 수 - userId: {}, count: {}", userId, likesCount);
            log.info("어제 새로 매칭된 수 - userId: {}, count: {}", userId, matchesCount);

            // 어제 받은 메시지 수
            int messagesCount = 0;
            try {
                messagesCount = chatApiClient.getYesterdayMessageCount(userId);
                log.info("어제 받은 메시지 수 - userId: {}, count: {}", userId, messagesCount);
            } catch (Exception e) {
                log.error("메시지 수 조회 실패 - userId: {}, error: {}", userId, e.getMessage());
                // 메시지 수 조회 실패 시 0으로 처리
            }

            UserActivitySummaryResponse response = UserActivitySummaryResponse.of(likesCount, matchesCount, messagesCount);
            log.info("사용자 활동 요약 계산 완료 - userId: {}, response: {}", userId, response);

            return response;

        } catch (Exception e) {
            log.error("사용자 활동 요약 계산 실패 - userId: {}, error: {}", userId, e.getMessage(), e);
            // 실패 시 모든 카운트를 0으로 반환
            return UserActivitySummaryResponse.of(0, 0, 0);
        }
    }
}
