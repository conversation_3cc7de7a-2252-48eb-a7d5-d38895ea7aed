package org.kiru.user.userlike.repository;

import jakarta.persistence.QueryHint;
import java.util.List;
import java.util.Optional;
import org.kiru.core.user.userlike.domain.LikeStatus;
import org.kiru.core.user.userlike.entity.UserLikeJpaEntity;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface UserLikeJpaRepository extends JpaRepository<UserLikeJpaEntity, Long>,  UserLikeRepository<UserLikeJpaEntity,Long>  {
    @Query("SELECT ul FROM UserLikeJpaEntity ul WHERE ul.userId = :userId AND ul.likedUserId = :likedUserId ORDER BY ul.updatedAt DESC LIMIT 1")
    Optional<UserLikeJpaEntity> findByUserIdAndLikedUserId(@Param("userId") Long userId, @Param("likedUserId") Long likedUserId);

    @Query("SELECT ul.userId FROM UserLikeJpaEntity ul WHERE ul.likedUserId = :likedUserId AND ul.isMatched = false GROUP BY ul.userId ORDER BY COUNT(ul.likedUserId) DESC")
    @QueryHints(value = {
            @QueryHint(name = "org.hibernate.readOnly", value = "true"),
            @QueryHint(name = "org.hibernate.fetchSize", value = "150"),
            @QueryHint(name = "jakarta.persistence.query.timeout", value = "5000")
    })
    List<Long> findAllLikeMeUserIdAndNotMatchedByLikedUserId(@Param("likedUserId") Long likedUserId, Pageable pageable);

    @Query("SELECT ul.likedUserId FROM UserLikeJpaEntity ul WHERE ul.userId = :userId AND ul.isMatched = true")
    @QueryHints(value = {
            @QueryHint(name = "org.hibernate.readOnly", value = "true"),
            @QueryHint(name = "org.hibernate.fetchSize", value = "150"),
            @QueryHint(name = "jakarta.persistence.query.timeout", value = "5000")

    })
    List<Long> findAllMatchedUserIdByUserId(@Param("userId") Long userId);

    @Query("SELECT ul.likedUserId FROM UserLikeJpaEntity ul WHERE ul.userId = :userId")
    @QueryHints(value = {
            @QueryHint(name = "org.hibernate.readOnly", value = "true"),
            @QueryHint(name = "org.hibernate.fetchSize", value = "150"),
            @QueryHint(name = "jakarta.persistence.query.timeout", value = "5000")
    })
    List<Long> findAllLikedUserIdByUserId(@Param("userId") Long userId);

    @Query("SELECT ul.likedUserId From UserLikeJpaEntity ul WHERE ul.userId = :userId AND ul.likeStatus = :status ORDER BY ul.updatedAt ASC")
    @QueryHints(value = {
            @QueryHint(name = "org.hibernate.readOnly", value = "true"),
            @QueryHint(name = "org.hibernate.fetchSize", value = "150"),
            @QueryHint(name = "jakarta.persistence.query.timeout", value = "5000")
    })
    List<Long> findAllLikedUserIdByUserIdAndLikeStatus(@Param("userId") Long userId, @Param("status") LikeStatus likeStatus);

    @Query("SELECT ul FROM UserLikeJpaEntity ul WHERE ul.userId = :likedUserId AND ul.likedUserId = :userId AND ul.likeStatus = :status ORDER BY ul.updatedAt DESC LIMIT 1")
    UserLikeJpaEntity findOppositeLike(@Param("likedUserId") Long likedUserId, @Param("userId") Long userId, @Param("status") LikeStatus status);

    @Query("SELECT ul.userId FROM UserLikeJpaEntity ul WHERE ul.likeStatus = 'LIKE' GROUP BY ul.userId ORDER BY COUNT(ul.likedUserId) DESC")
    @QueryHints(value = {
            @QueryHint(name = "org.hibernate.readOnly", value = "true"),
            @QueryHint(name = "org.hibernate.fetchSize", value = "150"),
            @QueryHint(name = "jakarta.persistence.query.timeout", value = "5000")

    })
    List<Long> findPopularUserId(Pageable pageable);

    @Query("SELECT ul FROM UserLikeJpaEntity ul WHERE ul.userId = :userId AND ul.isMatched = true")
     List<UserLikeJpaEntity> findAllMatchedUsersWithMatchedTime(Long userId, Pageable pageable);

    // 어제 받은 좋아요 수 계산
    @Query("SELECT COUNT(ul) FROM UserLikeJpaEntity ul WHERE ul.likedUserId = :userId AND ul.likeStatus = 'LIKE' AND DATE(ul.createdAt) = DATE(:yesterday)")
    int countLikesReceivedYesterday(@Param("userId") Long userId, @Param("yesterday") java.time.LocalDate yesterday);

    // 어제 새로 매칭된 수 계산
    @Query("SELECT COUNT(ul) FROM UserLikeJpaEntity ul WHERE ul.userId = :userId AND ul.isMatched = true AND DATE(ul.updatedAt) = DATE(:yesterday)")
    int countMatchesYesterday(@Param("userId") Long userId, @Param("yesterday") java.time.LocalDate yesterday);

    // 테스트용: 오늘 받은 좋아요 수 계산
    @Query("SELECT COUNT(ul) FROM UserLikeJpaEntity ul WHERE ul.likedUserId = :userId AND ul.likeStatus = 'LIKE' AND DATE(ul.createdAt) = CURDATE()")
    int countLikesReceivedToday(@Param("userId") Long userId);

    // 테스트용: 오늘 새로 매칭된 수 계산
    @Query("SELECT COUNT(ul) FROM UserLikeJpaEntity ul WHERE ul.userId = :userId AND ul.isMatched = true AND DATE(ul.updatedAt) = CURDATE()")
    int countMatchesToday(@Param("userId") Long userId);
}