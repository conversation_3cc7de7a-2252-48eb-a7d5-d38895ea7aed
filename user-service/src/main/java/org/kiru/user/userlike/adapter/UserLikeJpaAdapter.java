package org.kiru.user.userlike.adapter;

import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQueryFactory;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.kiru.core.user.user.entity.QUserJpaEntity;
import org.kiru.core.user.userPortfolioItem.entity.QUserPortfolioImg;
import org.kiru.core.user.userlike.domain.LikeStatus;
import org.kiru.core.user.userlike.domain.UserLike;
import org.kiru.core.user.userlike.entity.QUserLikeJpaEntity;
import org.kiru.core.user.userlike.entity.UserLikeJpaEntity;
import org.kiru.user.admin.dto.AdminLikeUserResponse.AdminLikeUserDto;
import org.kiru.user.admin.dto.MatchedUserResponse;
import org.kiru.user.admin.service.out.UserLikeAdminUseCase;
import org.kiru.user.userBlock.repository.UserBlockJpaRepository;
import org.kiru.user.userlike.dto.Longs;
import org.kiru.user.userlike.repository.UserLikeJpaRepository;
import org.kiru.user.userlike.service.out.GetUserLikeQuery;
import org.kiru.user.userlike.service.out.SendLikeOrDislikeUseCase;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Repository
@Transactional(readOnly = true)
public class UserLikeJpaAdapter implements SendLikeOrDislikeUseCase, GetUserLikeQuery, UserLikeAdminUseCase {

    private final UserLikeJpaRepository userLikeRepository;
    private final UserBlockJpaRepository userBlockRepository;
    private final JPAQueryFactory queryFactory;

    @Transactional
    @Override
    public UserLike sendLikeOrDislike(Long userId, Long likedUserId, LikeStatus newStatus) {
        UserLikeJpaEntity current = findOrCreate(userId, likedUserId, newStatus);
        updateLikeStatus(current, newStatus);

        if (isBlockedByTarget(likedUserId, userId)) {
            userLikeRepository.save(current);
            return toDomain(current);
        }

        if (shouldMatch(current, likedUserId, userId)) {
            applyMatch(current, likedUserId, userId);
        }

        userLikeRepository.save(current);
        return toDomain(current);
    }

    private void updateLikeStatus(UserLikeJpaEntity current, LikeStatus status){
        current.likeStatus(status);
        if(status == LikeStatus.DISLIKE){
            current.setMatched(false);
        }
    }

    private UserLikeJpaEntity findOrCreate(Long userId, Long likedUserId, LikeStatus status) {
        return userLikeRepository.findByUserIdAndLikedUserId(userId, likedUserId)
            .orElse(UserLikeJpaEntity.of(userId, likedUserId, status, false));
    }

    private boolean isBlockedByTarget(Long targetId, Long sourceId) {
        Optional<?> block = userBlockRepository.findByUserIdAndBlockedUserId(targetId, sourceId);
        return block.isPresent();
    }

    private boolean shouldMatch(UserLikeJpaEntity current, Long targetId, Long sourceId) {
        if(current.getLikeStatus() == LikeStatus.DISLIKE) return false;
        if (current.isMatched() && current.getLikeStatus() == LikeStatus.LIKE){
            return false;
        }
        return userLikeRepository.findOppositeLike(targetId, sourceId, LikeStatus.LIKE) != null;
    }

    private void applyMatch(UserLikeJpaEntity current, Long targetId, Long sourceId) {
        current.setMatched(true);
        UserLikeJpaEntity opposite = userLikeRepository
            .findOppositeLike(targetId, sourceId, LikeStatus.LIKE);
        opposite.setMatched(true);
        userLikeRepository.save(opposite);
    }

    private UserLike toDomain(UserLikeJpaEntity entity) {
        return entity.toDomain();
    }

    @Override
    @Cacheable(value = "popularIds", key = "#pageable.pageNumber", unless = "#result==null")
    public Longs getPopularUserId(Pageable pageable) {
        return new Longs(userLikeRepository.findPopularUserId(pageable));
    }

    @Override
    public List<Long> findAllMatchedUserIdByUserId(Long userId) {
        return userLikeRepository.findAllMatchedUserIdByUserId(userId);
    }

    @Override
    public List<Long> findAllLikedUserIdByUserId(Long userId) {
        return userLikeRepository.findAllLikedUserIdByUserId(userId);
    }

    @Override
    public List<Long> findAllLikedUserIdByUserIdAndLikeStatus(Long userId, LikeStatus likeStatus) {
        return userLikeRepository.findAllLikedUserIdByUserIdAndLikeStatus(userId, likeStatus);
    }

    @Override
    @Cacheable(value = "like", key = "#userId+'-'+#pageable.pageNumber", unless = "#result == null")
    public List<Long> findAllLikeMeUserIdAndNotMatchedByLikedUserId(Long userId, Pageable pageable) {
        return userLikeRepository.findAllLikeMeUserIdAndNotMatchedByLikedUserId(userId, pageable);
    }

    @Override
    public List<AdminLikeUserDto> findUserLikesInternal(Pageable pageable, Long userId, String name, boolean isLiked) {
        QUserJpaEntity qUserJpaEntity = QUserJpaEntity.userJpaEntity;
        QUserLikeJpaEntity qUserLike = QUserLikeJpaEntity.userLikeJpaEntity;
        QUserPortfolioImg qUserPortfolioImg = QUserPortfolioImg.userPortfolioImg;
        List<AdminLikeUserDto> likes = queryFactory.select(Projections.constructor(AdminLikeUserDto.class,
                        qUserJpaEntity.id,
                        qUserJpaEntity.username,
                        qUserPortfolioImg.portfolioImageUrl,
                        qUserLike.createdAt))
                .from(qUserLike)
                .innerJoin(qUserJpaEntity)
                .on(isLiked ? qUserLike.userId.eq(qUserJpaEntity.id) : qUserLike.likedUserId.eq(qUserJpaEntity.id))
                .leftJoin(qUserPortfolioImg)
                .on(qUserJpaEntity.id.eq(qUserPortfolioImg.userId).and(qUserPortfolioImg.sequence.eq(1)))
                .where((isLiked ? qUserLike.likedUserId.eq(userId) : qUserLike.userId.eq(userId))
                        .and(name != null ? qUserJpaEntity.username.containsIgnoreCase(name) : null))
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .fetch();
        return likes;
    }

    @Override
    public Map<Long, MatchedUserResponse> findAllMatchedUserIdWithMatchedTime(Long userId, Pageable pageable) {
        return userLikeRepository.findAllMatchedUsersWithMatchedTime(userId, pageable).stream()
                .map(MatchedUserResponse::of)
                .collect(Collectors.toMap(MatchedUserResponse::userId, matchedUser -> matchedUser));
    }
}
