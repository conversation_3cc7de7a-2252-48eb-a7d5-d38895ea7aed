package org.kiru.user.userlike.service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import org.kiru.core.chat.chatroom.domain.ChatRoomType;
import org.kiru.core.exception.ConflictException;
import org.kiru.core.exception.code.FailureCode;
import org.kiru.core.user.userlike.domain.LikeStatus;
import org.kiru.user.common.RedissonLockService;
import org.kiru.user.portfolio.dto.res.UserPortfolioResDto;
import org.kiru.user.user.api.ChatApiClient;
import org.kiru.user.userlike.api.CreateChatRoomRequest;
import org.kiru.user.userlike.api.CreateChatRoomResponse;
import org.kiru.user.userlike.dto.res.LikeResponse;
import org.kiru.user.userlike.service.out.GetMatchedUserPortfolioQuery;
import org.kiru.user.userlike.service.out.SendLikeOrDislikeUseCase;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class UserLikeService {
    private final SendLikeOrDislikeUseCase sendLikeOrDislikeUseCase;
    private final GetMatchedUserPortfolioQuery getMatchedUserPortfolioQuery;
    private final ChatApiClient chatRoomCreateApiClient;
    private final MatchNotificationService matchNotificationService;
    private final Executor virtualThreadExecutor;
    private final RedissonLockService redissonLockService;

    public UserLikeService(
            @Qualifier("userLikeJpaAdapter") SendLikeOrDislikeUseCase sendLikeOrDislikeUseCase,
            GetMatchedUserPortfolioQuery getMatchedUserPortfolioQuery,
            ChatApiClient chatRoomCreateApiClient,
            MatchNotificationService matchNotificationService,
            Executor virtualThreadExecutor,
        RedissonLockService redissonLockService) {
        this.sendLikeOrDislikeUseCase = sendLikeOrDislikeUseCase;
        this.getMatchedUserPortfolioQuery = getMatchedUserPortfolioQuery;
        this.chatRoomCreateApiClient = chatRoomCreateApiClient;
        this.matchNotificationService = matchNotificationService;
        this.virtualThreadExecutor = virtualThreadExecutor;
        this.redissonLockService = redissonLockService;
    }

    public LikeResponse sendLikeOrDislike(Long userId, Long likedUserId, LikeStatus status) {
        String lockKey = buildLockKey(userId, likedUserId);
        boolean lockAcquired = redissonLockService.tryAcquireLock(lockKey);

        if (!lockAcquired) throw new ConflictException(FailureCode.DUPLICATE_LOCK);

        try{
            boolean isMatched = sendLikeOrDislikeUseCase.sendLikeOrDislike(userId, likedUserId, status).isMatched();
            if (isMatched) {
                return matchedPortfolioAndChatRoom(userId, likedUserId);
            }
        } finally {
            redissonLockService.release(lockKey);
        }
        return LikeResponse.of(false, null, null);
    }

    private LikeResponse matchedPortfolioAndChatRoom(long userId, long likedUserId){
        log.info("User matched with userId: {} and likedUserId: {}", userId, likedUserId);
        CompletableFuture<List<UserPortfolioResDto>> portfolioFuture = getMatchedUserPortfolioQuery(userId, likedUserId);
        CreateChatRoomResponse newChatRoom = createChatRoomResponse(userId, likedUserId);

        CompletableFuture.runAsync(() ->
                matchNotificationService.sendMatchNotifications(userId, likedUserId, newChatRoom.getChatRoomId()),
            virtualThreadExecutor
        );

        List<UserPortfolioResDto> portfolios = portfolioFuture.join();
        return LikeResponse.of(true, portfolios, newChatRoom.getChatRoomId());
    }

    private String buildLockKey(Long userId1, Long userId2) {
        return (userId1 <= userId2)
            ? "lock:userLike:create:" + userId1 + ":" + userId2
            : "lock:userLike:create:" + userId2 + ":" + userId1;
    }

    private CompletableFuture<List<UserPortfolioResDto>> getMatchedUserPortfolioQuery(long userId, long likedUserId){
        return CompletableFuture.supplyAsync(
            () -> getMatchedUserPortfolioQuery.findByUserIds(List.of(userId, likedUserId)),
            virtualThreadExecutor);
    }

    private CreateChatRoomResponse createChatRoomResponse(long userId, long likedUserId){
        return chatRoomCreateApiClient.createRoom(userId,
            CreateChatRoomRequest.of("CONTACTO MANAGER", ChatRoomType.PRIVATE, userId,
                likedUserId));
    }
}
