package org.kiru.user.common;

import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class RedissonLockService {
  private final RedissonClient redissonClient;

  public boolean tryAcquireLock(String lockKey){
    RLock lock = redissonClient.getLock(lockKey);

    try{
      return lock.tryLock(4, 3, TimeUnit.SECONDS);
    } catch (InterruptedException e) {
      log.warn("락 획득 중 인터럽트 발생: {}", lockKey, e);
      return false;
    }
  }

  public void release(String lockKey) {
    RLock lock = redissonClient.getLock(lockKey);
    if (lock.isHeldByCurrentThread()){
      lock.unlock();
    }
  }
}
