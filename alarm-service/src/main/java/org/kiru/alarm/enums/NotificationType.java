package org.kiru.alarm.enums;

public enum NotificationType {
    DAILY_ACTIVITY("daily_activity", "일일 활동 요약"),
    MATCH_NOTIFICATION("match", "새로운 매칭"),
    MESSAGE_NOTIFICATION("message", "새로운 메시지"),
    LIKE_NOTIFICATION("like", "새로운 좋아요");

    private final String type;
    private final String description;

    NotificationType(String type, String description) {
        this.type = type;
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }
}
