package org.kiru.alarm.enums;

public enum NotificationType {
    DAILY_ACTIVITY(
        "daily_activity",
        "일일 활동 요약",
        "어제 CONTACTO에서 당신에게 온 소식들입니다.",
        "❤️ 받은 좋아요: %d개\n🤝 새 매칭: %d건\n💌 메시지 도착: %d건\n창작자들의 관심이 당신에게 쏟아졌어요.\n오늘은 어떤 인연이 기다릴까요?"
    ),
    MATCH_NOTIFICATION(
        "match",
        "새로운 매칭",
        "새로운 매칭이 있습니다!",
        "축하합니다! 새로운 인연이 시작되었어요. 💕"
    ),
    MESSAGE_NOTIFICATION(
        "message",
        "새로운 메시지",
        "새로운 메시지가 도착했습니다.",
        "누군가 당신에게 메시지를 보냈어요! 💌"
    ),
    LIKE_NOTIFICATION(
        "like",
        "새로운 좋아요",
        "새로운 좋아요를 받았습니다!",
        "누군가 당신에게 관심을 보이고 있어요! ❤️"
    );

    private final String type;
    private final String description;
    private final String titleTemplate;
    private final String bodyTemplate;

    NotificationType(String type, String description, String titleTemplate, String bodyTemplate) {
        this.type = type;
        this.description = description;
        this.titleTemplate = titleTemplate;
        this.bodyTemplate = bodyTemplate;
    }

    public String getType() {
        return type;
    }

    public String getDescription() {
        return description;
    }

    public String getTitleTemplate() {
        return titleTemplate;
    }

    public String getBodyTemplate() {
        return bodyTemplate;
    }

    // 일일 활동 알림용 메시지 생성
    public String formatDailyActivityMessage(int likesCount, int matchesCount, int messagesCount) {
        if (this != DAILY_ACTIVITY) {
            throw new IllegalStateException("This method is only for DAILY_ACTIVITY type");
        }
        return String.format(bodyTemplate, likesCount, matchesCount, messagesCount);
    }
}
