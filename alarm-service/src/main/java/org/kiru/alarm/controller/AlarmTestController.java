package org.kiru.alarm.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kiru.alarm.service.ScheduledAlarmService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/alarm/test")
public class AlarmTestController {
    
    private final ScheduledAlarmService scheduledAlarmService;
    
    @PostMapping("/daily-summary")
    public ResponseEntity<String> testDailySummary() {
        log.info("일일 알림 테스트 시작");
        try {
            scheduledAlarmService.executeDailyNotificationSummary();
            return ResponseEntity.ok("일일 알림 테스트 완료");
        } catch (Exception e) {
            log.error("일일 알림 테스트 실패", e);
            return ResponseEntity.internalServerError().body("일일 알림 테스트 실패: " + e.getMessage());
        }
    }
}
