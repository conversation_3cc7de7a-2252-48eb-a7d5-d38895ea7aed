package org.kiru.alarm.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kiru.alarm.dto.UserActivitySummaryDto;
import org.kiru.alarm.repository.DeviceRepository;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class ScheduledAlarmService {

    private final AlarmService alarmService;
    private final DeviceRepository deviceRepository;
    private final UserActivityService userActivityService;

    // 매일 오전 9시에 실행 (cron = "0 0 9 * * ?")
    @Scheduled(cron = "0 0 9 * * ?")
    public void sendDailyNotificationSummary() {
        log.info("데일리 알림 요약 발송 시작");
        List<Long> userIds = deviceRepository.findDistinctUserIds();
        log.info("데일리 알림 대상 사용자 수: {}", userIds.size());

        for (Long userId : userIds) {
            UserActivitySummaryDto activitySummary = userActivityService.getUserActivitySummary(userId);

            int likesCount = activitySummary.getLikesCount();
            int matchesCount = activitySummary.getMatchesCount();
            int messagesCount = activitySummary.getMessagesCount();

            // 활동이 없는 경우 알림을 보내지 않음
            if (likesCount == 0 && matchesCount == 0 && messagesCount == 0) {
                log.info("활동이 없어 알림을 보내지 않음 - userId: {}", userId);
                continue;
            }

            String title = "어제 CONTACTO에서 당신에게 온 소식들입니다.";
            String body = String.format(
                    ":하트2: 받은 좋아요: %d개\n:악수: 새 매칭: %d건\n:연애편지: 메시지 도착: %d건\n창작자들의 관심이 당신에게 쏟아졌어요.\n오늘은 어떤 인연이 기다릴까요?",
                    likesCount, matchesCount, messagesCount
            );

            Map<String, String> content = new HashMap<>();
            content.put("notificationType", "dailySummary");
            // 사용자별 활동 데이터를 content에도 추가할 수 있습니다.
            content.put("likesCount", String.valueOf(likesCount));
            content.put("matchesCount", String.valueOf(matchesCount));
            content.put("messagesCount", String.valueOf(messagesCount));

            try {
                alarmService.sendMessageToUser(userId, title, body, content);
                log.info("데일리 알림 발송 성공 - userId: {}, likes: {}, matches: {}, messages: {}", userId, likesCount, matchesCount, messagesCount);
            } catch (Exception e) {
                log.error("데일리 알림 발송 실패 - userId: {}. Error: {}", userId, e.getMessage(), e);
            }
        }
        log.info("데일리 알림 요약 발송 완료");
    }
}
