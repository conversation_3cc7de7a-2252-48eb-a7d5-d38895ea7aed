package org.kiru.alarm.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kiru.alarm.client.UserActivityClient;
import org.kiru.alarm.dto.UserActivitySummaryDto;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class UserActivityService {

    private final UserActivityClient userActivityClient;

    public UserActivitySummaryDto getUserActivitySummary(Long userId) {
        log.info("사용자 활동 요약 요청 - userId: {}", userId);
        try {
            UserActivitySummaryDto summary = userActivityClient.getUserActivitySummary(userId);
            log.info("사용자 활동 요약 수신 - userId: {}, likes: {}, matches: {}, messages: {}", 
                userId, summary.getLikesCount(), summary.getMatchesCount(), summary.getMessagesCount());
            return summary;
        } catch (Exception e) {
            log.error("사용자 활동 요약 조회 실패 - userId: {}. Error: {}", userId, e.getMessage(), e);
            // 실패 시 기본값 또는 특정 로직 처리 (예: 모든 카운트를 0으로 반환)
            return new UserActivitySummaryDto(0, 0, 0); 
        }
    }
}
