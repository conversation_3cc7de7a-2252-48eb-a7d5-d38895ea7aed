package org.kiru.alarm.client;

import org.kiru.alarm.dto.UserActivitySummaryDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

// "user-service"는 실제 사용자 서비스의 Eureka 등록 이름 또는 URL이어야 합니다.
@FeignClient(name = "user-service", path = "/api/v1/internal/users")
public interface UserActivityClient {

    @GetMapping("/{userId}/activity-summary")
    UserActivitySummaryDto getUserActivitySummary(@PathVariable("userId") Long userId);
    
}

